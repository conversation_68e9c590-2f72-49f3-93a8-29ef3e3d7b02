import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

const PageTitle = () => {
  const location = useLocation();

  // Page title mapping
  const pageTitles = {
    '/': 'Home - Maruti iT Zone | Leading IT Solutions Provider',
    '/about': 'About Us - Maruti iT Zone | Our Story & Mission',
    '/services': 'Our Services - Maruti iT Zone | IT Solutions & Development',
    '/services/web-development': 'Web Development Services - Maruti iT Zone',
    '/services/mobile-development': 'Mobile App Development - Maruti iT Zone',
    '/services/cloud-solutions': 'Cloud Solutions - Maruti iT Zone',
    '/services/digital-marketing': 'Digital Marketing Services - Maruti iT Zone',
    '/services/ui-ux-design': 'UI/UX Design Services - Maruti iT Zone',
    '/services/consulting': 'IT Consulting Services - Maruti iT Zone',
    '/portfolio': 'Portfolio - Maruti iT Zone | Our Work & Projects',
    '/team': 'Our Team - Maruti iT Zone | Meet Our Experts',
    '/careers': 'Careers - Maruti iT Zone | Join Our Team',
    '/contact': 'Contact Us - Maruti iT Zone | Get In Touch',
    '/login': 'Login - Maruti iT Zone',
    '/register': 'Register - Maruti iT Zone',
    '/dashboard': 'Dashboard - Maruti iT Zone',
    '/privacy-policy': 'Privacy Policy - Maruti iT Zone',
    '/terms-of-service': 'Terms of Service - Maruti iT Zone',
    '/sitemap': 'Sitemap - Maruti iT Zone'
  };

  useEffect(() => {
    const title = pageTitles[location.pathname] || 'Maruti iT Zone - Leading IT Solutions Provider';
    document.title = title;
  }, [location.pathname]);

  return null;
};

export default PageTitle;
