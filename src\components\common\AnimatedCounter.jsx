import { useState, useEffect, useRef } from 'react';

const AnimatedCounter = ({
  end,
  duration = 2000,
  suffix = '',
  prefix = '',
  title,
  description,
  icon: IconComponent,
  color = 'var(--primary)'
}) => {
  const [count, setCount] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);
  const counterRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasAnimated) {
          setIsVisible(true);
          setHasAnimated(true);
        }
      },
      { threshold: 0.3 }
    );

    if (counterRef.current) {
      observer.observe(counterRef.current);
    }

    return () => {
      if (counterRef.current) {
        observer.unobserve(counterRef.current);
      }
    };
  }, [hasAnimated]);

  useEffect(() => {
    if (!isVisible) return;

    let startTime;
    let animationFrame;

    const animate = (timestamp) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);
      
      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentCount = Math.floor(easeOutQuart * end);
      
      setCount(currentCount);

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isVisible, end, duration]);

  return (
    <div 
      ref={counterRef}
      className="animated-counter text-center h-100 d-flex flex-column justify-content-center"
      style={{
        padding: '2rem 1rem',
        borderRadius: '15px',
        background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(0, 0, 0, 0.1)',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
        transition: 'all 0.3s ease',
        transform: isVisible ? 'translateY(0)' : 'translateY(20px)',
        opacity: isVisible ? 1 : 0
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'translateY(-5px)';
        e.currentTarget.style.boxShadow = '0 12px 40px rgba(0, 0, 0, 0.15)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translateY(0)';
        e.currentTarget.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.1)';
      }}
    >
      {/* Icon */}
      {IconComponent && (
        <div 
          className="mb-3"
          style={{
            fontSize: '3rem',
            color: color,
            filter: 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2))'
          }}
        >
          <IconComponent />
        </div>
      )}

      {/* Counter */}
      <div 
        className="counter-number mb-2"
        style={{
          fontSize: '3.5rem',
          fontWeight: '800',
          background: `linear-gradient(135deg, ${color} 0%, ${color}CC 100%)`,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          lineHeight: '1',
          fontFamily: '"Inter", "Segoe UI", sans-serif'
        }}
      >
        {prefix}{count}{suffix}
      </div>

      {/* Title */}
      <h4 
        className="counter-title mb-2"
        style={{
          color: 'var(--text-dark)',
          fontSize: '1.25rem',
          fontWeight: '600',
          margin: '0'
        }}
      >
        {title}
      </h4>

      {/* Description */}
      {description && (
        <p 
          className="counter-description mb-0"
          style={{
            color: 'var(--text-muted)',
            fontSize: '0.9rem',
            lineHeight: '1.4'
          }}
        >
          {description}
        </p>
      )}
    </div>
  );
};

export default AnimatedCounter;
