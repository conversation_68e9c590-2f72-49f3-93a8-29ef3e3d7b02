import { useState } from 'react';
import { Container, Row, Col, Card, CardBody, Form, FormGroup, Label, Input, Button, Alert, InputGroup, InputGroupText } from 'reactstrap';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { FaEye, FaEyeSlash, FaEnvelope, FaLock, FaSignInAlt, FaGoogle, FaFacebook, FaTwitter } from 'react-icons/fa';
import SEO from '../../components/common/SEO';


const Login = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState({});
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState('');

  const { login, loading } = useAuth();
  const navigate = useNavigate();

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setMessage('');

    if (!validateForm()) {
      return;
    }

    try {
      const result = await login(formData);

      if (result.success) {
        setMessage(result.message);
        setMessageType('success');

        // Navigate based on user role
        setTimeout(() => {
          if (result.user.role === 'admin') {
            navigate('/admin');
          } else {
            navigate('/dashboard');
          }
        }, 1500);
      } else {
        setMessage(result.message);
        setMessageType('danger');
      }
    } catch (err) {
      setMessage('An error occurred during login. Please try again.');
      setMessageType('danger');
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="login-page" style={{
      minHeight: '100vh',
      paddingTop: '2rem',
      paddingBottom: '2rem',
      background: 'linear-gradient(135deg, rgba(255, 87, 34, 0.05) 0%, rgba(33, 150, 243, 0.05) 100%)',
      position: 'relative'
    }}>
      {/* Background Pattern */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundImage: `
          radial-gradient(circle at 20% 20%, rgba(255, 87, 34, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 80% 80%, rgba(33, 150, 243, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 40% 60%, rgba(255, 193, 7, 0.05) 0%, transparent 50%)
        `,
        zIndex: 1
      }} />

      <SEO
        title="Login - Maruti iT Zone"
        description="Login to your Maruti iT Zone account to access your dashboard and manage your projects."
        keywords="login, account, dashboard, Maruti iT Zone"
        url="https://marutiitzone.com/login"
      />
      <Container style={{ position: 'relative', zIndex: 2 }}>
        <Row className="justify-content-center align-items-center" style={{ minHeight: '90vh' }}>
          <Col lg={5} md={7} sm={9}>
            <Card className="shadow-lg border-0" style={{
              borderRadius: '1.5rem',
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.2)'
            }}>
              <CardBody className="p-5">
                {/* Header */}
                <div className="text-center mb-4">
                  <div
                    className="mb-3 mx-auto"
                    style={{
                      width: '70px',
                      height: '70px',
                      background: 'linear-gradient(135deg, rgba(255, 87, 34, 0.1) 0%, rgba(33, 150, 243, 0.1) 100%)',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: '#FF5722',
                      fontSize: '1.8rem',
                      fontWeight: 'bold',
                      border: '2px solid rgba(255, 87, 34, 0.2)'
                    }}
                  >
                    <FaSignInAlt />
                  </div>
                  <h2 className="mb-2 text-gradient" style={{ fontWeight: '700' }}>Welcome Back</h2>
                  <p className="text-muted mb-0">Sign in to your Maruti iT Zone account</p>
                </div>

                {/* Alert Messages */}
                {message && (
                  <Alert color={messageType} className="mb-4">
                    {message}
                  </Alert>
                )}

                {/* Login Form */}
                <Form onSubmit={handleSubmit}>
                  {/* Email */}
                  <FormGroup className="mb-3">
                    <Label for="email" className="fw-semibold">Email Address</Label>
                    <InputGroup>
                      <InputGroupText style={{ background: '#F8FAFC', border: '1px solid #E2E8F0' }}>
                        <FaEnvelope className="text-muted" />
                      </InputGroupText>
                      <Input
                        type="email"
                        name="email"
                        id="email"
                        value={formData.email}
                        onChange={handleChange}
                        placeholder="Enter your email"
                        invalid={!!errors.email}
                        style={{
                          padding: '0.75rem',
                          border: errors.email ? '1px solid #EF4444' : '1px solid #E2E8F0',
                          borderLeft: 'none'
                        }}
                      />
                    </InputGroup>
                    {errors.email && <div className="text-danger small mt-1">{errors.email}</div>}
                  </FormGroup>

                  {/* Password */}
                  <FormGroup className="mb-3">
                    <Label for="password" className="fw-semibold">Password</Label>
                    <InputGroup>
                      <InputGroupText style={{ background: '#F8FAFC', border: '1px solid #E2E8F0' }}>
                        <FaLock className="text-muted" />
                      </InputGroupText>
                      <Input
                        type={showPassword ? 'text' : 'password'}
                        name="password"
                        id="password"
                        value={formData.password}
                        onChange={handleChange}
                        placeholder="Enter your password"
                        invalid={!!errors.password}
                        style={{
                          padding: '0.75rem',
                          border: errors.password ? '1px solid #EF4444' : '1px solid #E2E8F0',
                          borderLeft: 'none',
                          borderRight: 'none'
                        }}
                      />
                      <InputGroupText
                        style={{
                          background: '#F8FAFC',
                          border: errors.password ? '1px solid #EF4444' : '1px solid #E2E8F0',
                          cursor: 'pointer'
                        }}
                        onClick={togglePasswordVisibility}
                      >
                        {showPassword ? <FaEyeSlash className="text-muted" /> : <FaEye className="text-muted" />}
                      </InputGroupText>
                    </InputGroup>
                    {errors.password && <div className="text-danger small mt-1">{errors.password}</div>}
                  </FormGroup>

                  {/* Remember Me & Forgot Password */}
                  <div className="d-flex justify-content-between align-items-center mb-4">
                    <FormGroup check className="mb-0">
                      <Label check className="d-flex align-items-center">
                        <Input
                          type="checkbox"
                          name="rememberMe"
                          checked={formData.rememberMe}
                          onChange={handleChange}
                          className="me-2"
                          style={{ transform: 'scale(1.1)' }}
                        />
                        <span className="small">Remember me</span>
                      </Label>
                    </FormGroup>
                    <Link
                      to="/forgot-password"
                      className="small text-decoration-none"
                      style={{ color: '#FF5722' }}
                    >
                      Forgot password?
                    </Link>
                  </div>

                  {/* Submit Button */}
                  <Button
                    type="submit"
                    className="w-100 mb-3"
                    size="lg"
                    disabled={loading}
                    style={{
                      background: 'linear-gradient(135deg, #FF5722 0%, #2196F3 100%)',
                      border: 'none',
                      borderRadius: '0.5rem',
                      padding: '0.75rem',
                      fontWeight: '600',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px'
                    }}
                  >
                    {loading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Signing in...
                      </>
                    ) : (
                      <>
                        <FaSignInAlt className="me-2" />
                        Sign In
                      </>
                    )}
                  </Button>
                </Form>

                {/* Social Login */}
                <div className="text-center mb-4">
                  <div className="d-flex align-items-center mb-3">
                    <hr className="flex-grow-1" />
                    <span className="px-3 text-muted small">Or continue with</span>
                    <hr className="flex-grow-1" />
                  </div>
                  <div className="d-flex justify-content-center gap-3">
                    <Button
                      color="light"
                      className="border"
                      style={{ width: '50px', height: '50px', borderRadius: '50%' }}
                    >
                      <FaGoogle style={{ color: '#DB4437' }} />
                    </Button>
                    <Button
                      color="light"
                      className="border"
                      style={{ width: '50px', height: '50px', borderRadius: '50%' }}
                    >
                      <FaFacebook style={{ color: '#4267B2' }} />
                    </Button>
                    <Button
                      color="light"
                      className="border"
                      style={{ width: '50px', height: '50px', borderRadius: '50%' }}
                    >
                      <FaTwitter style={{ color: '#1DA1F2' }} />
                    </Button>
                  </div>
                </div>

                {/* Register Link */}
                <div className="text-center mb-4">
                  <span className="text-muted">Don't have an account? </span>
                  <Link
                    to="/register"
                    className="text-decoration-none fw-semibold"
                    style={{ color: '#FF5722' }}
                  >
                    Create Account
                  </Link>
                </div>

                {/* Demo Credentials */}
                <div className="text-center">
                  <div className="bg-light p-3 rounded">
                    <small className="text-muted d-block mb-2">
                      <strong>Demo Credentials:</strong>
                    </small>
                    <small className="text-muted d-block">
                      <strong>Admin:</strong> <EMAIL> / admin123
                    </small>
                    <small className="text-muted d-block">
                      <strong>User:</strong> <EMAIL> / password123
                    </small>
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* Help Link */}
            <div className="text-center mt-4">
              <small className="text-white">
                Need help? Contact us at{' '}
                <a href="mailto:<EMAIL>" className="text-white text-decoration-none fw-semibold">
                  <EMAIL>
                </a>
              </small>
            </div>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default Login;
