import { useState } from 'react';
import { Container, Row, Col, Card, CardBody, Form, FormGroup, Label, Input, Button, Alert, InputGroup, InputGroupText } from 'reactstrap';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { FaEye, FaEyeSlash, FaUser, FaEnvelope, FaLock, FaUserPlus, FaGoogle, FaFacebook, FaTwitter, FaPhone } from 'react-icons/fa';
import SEO from '../../components/common/SEO';

const Register = () => {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    mobile: '',
    password: '',
    confirmPassword: '',
    agreeToTerms: false
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState({});
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState('');
  
  const { register, loading } = useAuth();
  const navigate = useNavigate();

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Full Name validation
    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Full name is required';
    } else if (formData.fullName.trim().length < 2) {
      newErrors.fullName = 'Name must be at least 2 characters';
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Mobile validation
    const mobileRegex = /^[6-9]\d{9}$/;
    if (!formData.mobile) {
      newErrors.mobile = 'Mobile number is required';
    } else if (!mobileRegex.test(formData.mobile)) {
      newErrors.mobile = 'Please enter a valid 10-digit mobile number';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password = 'Password must contain at least one uppercase letter, one lowercase letter, and one number';
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    // Terms validation
    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = 'You must agree to the terms and conditions';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setMessage('');
    
    if (!validateForm()) {
      return;
    }

    try {
      const result = await register({
        name: formData.name.trim(),
        email: formData.email.toLowerCase().trim(),
        password: formData.password
      });
      
      if (result.success) {
        setMessage(result.message);
        setMessageType('success');
        setTimeout(() => {
          navigate('/dashboard');
        }, 2000);
      } else {
        setMessage(result.message);
        setMessageType('danger');
      }
    } catch (err) {
      setMessage('Registration failed. Please try again.');
      setMessageType('danger');
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  return (
    <div className="register-page hero-section" style={{ minHeight: '100vh', paddingTop: '2rem', paddingBottom: '2rem' }}>
      <Container>
        <Row className="justify-content-center align-items-center" style={{ minHeight: '90vh' }}>
          <Col lg={5} md={7} sm={9}>
            <Card className="shadow-lg border-0" style={{ borderRadius: '1rem' }}>
              <CardBody className="p-5">
                {/* Header */}
                <div className="text-center mb-4">
                  <div 
                    className="mb-3 mx-auto"
                    style={{
                      width: '80px',
                      height: '80px',
                      background: 'linear-gradient(135deg, #FF5722 0%, #2196F3 100%)',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: '2rem',
                      fontWeight: 'bold'
                    }}
                  >
                    <FaUserPlus />
                  </div>
                  <h2 className="mb-2" style={{ color: '#1E293B', fontWeight: '700' }}>Create Account</h2>
                  <p className="text-muted mb-0">Join Maruti IT Zone today</p>
                </div>

                {/* Alert Messages */}
                {message && (
                  <Alert color={messageType} className="mb-4">
                    {message}
                  </Alert>
                )}

                {/* Registration Form */}
                <Form onSubmit={handleSubmit}>
                  {/* Full Name */}
                  <FormGroup className="mb-3">
                    <Label for="fullName" className="fw-semibold">Full Name</Label>
                    <InputGroup>
                      <InputGroupText style={{ background: '#F8FAFC', border: '1px solid #E2E8F0' }}>
                        <FaUser className="text-muted" />
                      </InputGroupText>
                      <Input
                        type="text"
                        name="fullName"
                        id="fullName"
                        value={formData.fullName}
                        onChange={handleChange}
                        placeholder="Enter your full name"
                        invalid={!!errors.fullName}
                        style={{
                          padding: '0.75rem',
                          border: errors.fullName ? '1px solid #EF4444' : '1px solid #E2E8F0',
                          borderLeft: 'none'
                        }}
                      />
                    </InputGroup>
                    {errors.fullName && <div className="text-danger small mt-1">{errors.fullName}</div>}
                  </FormGroup>

                  {/* Email */}
                  <FormGroup className="mb-3">
                    <Label for="email" className="fw-semibold">Email Address</Label>
                    <InputGroup>
                      <InputGroupText style={{ background: '#F8FAFC', border: '1px solid #E2E8F0' }}>
                        <FaEnvelope className="text-muted" />
                      </InputGroupText>
                      <Input
                        type="email"
                        name="email"
                        id="email"
                        value={formData.email}
                        onChange={handleChange}
                        placeholder="Enter your email"
                        invalid={!!errors.email}
                        style={{ 
                          padding: '0.75rem',
                          border: errors.email ? '1px solid #EF4444' : '1px solid #E2E8F0',
                          borderLeft: 'none'
                        }}
                      />
                    </InputGroup>
                    {errors.email && <div className="text-danger small mt-1">{errors.email}</div>}
                  </FormGroup>

                  {/* Mobile Number */}
                  <FormGroup className="mb-3">
                    <Label for="mobile" className="fw-semibold">Mobile Number</Label>
                    <InputGroup>
                      <InputGroupText style={{ background: '#F8FAFC', border: '1px solid #E2E8F0' }}>
                        <FaPhone className="text-muted" />
                      </InputGroupText>
                      <Input
                        type="tel"
                        name="mobile"
                        id="mobile"
                        value={formData.mobile}
                        onChange={handleChange}
                        placeholder="Enter your mobile number"
                        invalid={!!errors.mobile}
                        style={{
                          padding: '0.75rem',
                          border: errors.mobile ? '1px solid #EF4444' : '1px solid #E2E8F0',
                          borderLeft: 'none'
                        }}
                      />
                    </InputGroup>
                    {errors.mobile && <div className="text-danger small mt-1">{errors.mobile}</div>}
                  </FormGroup>

                  {/* Password */}
                  <FormGroup className="mb-3">
                    <Label for="password" className="fw-semibold">Password</Label>
                    <InputGroup>
                      <InputGroupText style={{ background: '#F8FAFC', border: '1px solid #E2E8F0' }}>
                        <FaLock className="text-muted" />
                      </InputGroupText>
                      <Input
                        type={showPassword ? 'text' : 'password'}
                        name="password"
                        id="password"
                        value={formData.password}
                        onChange={handleChange}
                        placeholder="Create a strong password"
                        invalid={!!errors.password}
                        style={{ 
                          padding: '0.75rem',
                          border: errors.password ? '1px solid #EF4444' : '1px solid #E2E8F0',
                          borderLeft: 'none',
                          borderRight: 'none'
                        }}
                      />
                      <InputGroupText 
                        style={{ 
                          background: '#F8FAFC', 
                          border: errors.password ? '1px solid #EF4444' : '1px solid #E2E8F0',
                          cursor: 'pointer'
                        }}
                        onClick={togglePasswordVisibility}
                      >
                        {showPassword ? <FaEyeSlash className="text-muted" /> : <FaEye className="text-muted" />}
                      </InputGroupText>
                    </InputGroup>
                    {errors.password && <div className="text-danger small mt-1">{errors.password}</div>}
                  </FormGroup>

                  {/* Confirm Password */}
                  <FormGroup className="mb-3">
                    <Label for="confirmPassword" className="fw-semibold">Confirm Password</Label>
                    <InputGroup>
                      <InputGroupText style={{ background: '#F8FAFC', border: '1px solid #E2E8F0' }}>
                        <FaLock className="text-muted" />
                      </InputGroupText>
                      <Input
                        type={showConfirmPassword ? 'text' : 'password'}
                        name="confirmPassword"
                        id="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleChange}
                        placeholder="Confirm your password"
                        invalid={!!errors.confirmPassword}
                        style={{ 
                          padding: '0.75rem',
                          border: errors.confirmPassword ? '1px solid #EF4444' : '1px solid #E2E8F0',
                          borderLeft: 'none',
                          borderRight: 'none'
                        }}
                      />
                      <InputGroupText 
                        style={{ 
                          background: '#F8FAFC', 
                          border: errors.confirmPassword ? '1px solid #EF4444' : '1px solid #E2E8F0',
                          cursor: 'pointer'
                        }}
                        onClick={toggleConfirmPasswordVisibility}
                      >
                        {showConfirmPassword ? <FaEyeSlash className="text-muted" /> : <FaEye className="text-muted" />}
                      </InputGroupText>
                    </InputGroup>
                    {errors.confirmPassword && <div className="text-danger small mt-1">{errors.confirmPassword}</div>}
                  </FormGroup>

                  {/* Terms and Conditions */}
                  <FormGroup check className="mb-4">
                    <Label check className="d-flex align-items-start">
                      <Input 
                        type="checkbox" 
                        name="agreeToTerms"
                        checked={formData.agreeToTerms}
                        onChange={handleChange}
                        className="me-2 mt-1"
                        style={{ transform: 'scale(1.1)' }}
                      />
                      <span className="small">
                        I agree to the{' '}
                        <Link to="/terms" className="text-decoration-none" style={{ color: '#FF5722' }}>
                          Terms of Service
                        </Link>
                        {' '}and{' '}
                        <Link to="/privacy" className="text-decoration-none" style={{ color: '#FF5722' }}>
                          Privacy Policy
                        </Link>
                      </span>
                    </Label>
                    {errors.agreeToTerms && <div className="text-danger small mt-1">{errors.agreeToTerms}</div>}
                  </FormGroup>

                  {/* Submit Button */}
                  <Button 
                    type="submit" 
                    className="w-100 mb-3" 
                    size="lg"
                    disabled={loading}
                    style={{
                      background: 'linear-gradient(135deg, #FF5722 0%, #2196F3 100%)',
                      border: 'none',
                      borderRadius: '0.5rem',
                      padding: '0.75rem',
                      fontWeight: '600',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px'
                    }}
                  >
                    {loading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Creating Account...
                      </>
                    ) : (
                      <>
                        <FaUserPlus className="me-2" />
                        Create Account
                      </>
                    )}
                  </Button>
                </Form>

                {/* Social Login */}
                <div className="text-center mb-4">
                  <div className="d-flex align-items-center mb-3">
                    <hr className="flex-grow-1" />
                    <span className="px-3 text-muted small">Or continue with</span>
                    <hr className="flex-grow-1" />
                  </div>
                  <div className="d-flex justify-content-center gap-3">
                    <Button 
                      color="light" 
                      className="border"
                      style={{ width: '50px', height: '50px', borderRadius: '50%' }}
                    >
                      <FaGoogle style={{ color: '#DB4437' }} />
                    </Button>
                    <Button 
                      color="light" 
                      className="border"
                      style={{ width: '50px', height: '50px', borderRadius: '50%' }}
                    >
                      <FaFacebook style={{ color: '#4267B2' }} />
                    </Button>
                    <Button 
                      color="light" 
                      className="border"
                      style={{ width: '50px', height: '50px', borderRadius: '50%' }}
                    >
                      <FaTwitter style={{ color: '#1DA1F2' }} />
                    </Button>
                  </div>
                </div>

                {/* Login Link */}
                <div className="text-center">
                  <span className="text-muted">Already have an account? </span>
                  <Link 
                    to="/login" 
                    className="text-decoration-none fw-semibold"
                    style={{ color: '#FF5722' }}
                  >
                    Sign In
                  </Link>
                </div>
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default Register;
