
import {
  FaReact,
  FaNodeJs,
  FaPython,
  FaPhp,
  FaLaravel,
  FaAngular,
  FaVuejs,
  FaJava,
  FaHtml5,
  FaCss3Alt,
  FaBootstrap,
  FaAws
} from 'react-icons/fa';
import {
  SiMongodb,
  SiExpress,
  SiMysql,
  SiJavascript,
  SiTypescript,
  SiNextdotjs,
  SiDjango,
  SiFlask,
  SiTailwindcss,
  SiFlutter
} from 'react-icons/si';

const TechShowcase = () => {
  const techStacks = [
    {
      name: 'MERN Stack',
      description: 'MongoDB, Express.js, React, Node.js',
      icons: [SiMongodb, SiExpress, FaReact, FaNodeJs],
      colors: ['#47A248', '#000000', '#61DAFB', '#339933']
    },
    {
      name: 'MEAN Stack',
      description: 'MongoDB, Express.js, Angular, Node.js',
      icons: [Si<PERSON>ongodb, SiExpress, FaAngular, FaNodeJs],
      colors: ['#47A248', '#000000', '#DD0031', '#339933']
    },
    {
      name: 'Laravel PHP',
      description: 'Modern Web New PHP Framework ',
      icons: [FaLaravel, FaPhp, SiMysql, FaBootstrap],
      colors: ['#FF2D20', '#777BB4', '#4479A1', '#7952B3']
    },
    {
      name: 'Python Stack',
      description: 'Django, Flask, Frappe, AI/ML',
      icons: [FaPython, SiDjango, SiFlask, SiMysql],
      colors: ['#3776AB', '#092E20', '#000000', '#4479A1']
    },
    {
      name: 'Frontend',
      description: 'Modern Web Web UI Technologies',
      icons: [FaHtml5, FaCss3Alt, SiJavascript, SiTailwindcss],
      colors: ['#E34F26', '#1572B6', '#F7DF1E', '#06B6D4']
    },
    {
      name: 'Mobile App Dev',
      description: 'Modern Cross-Platform Apps',
      icons: [FaReact, SiFlutter, FaJava, SiTypescript],
      colors: ['#61DAFB', '#02569B', '#ED8B00', '#3178C6']
    }
  ];

  return (
    <div className="tech-showcase py-1">
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 justify-center">
        {techStacks.map((stack, stackIndex) => (
          <div
            key={stackIndex}
            className="tech-stack-card text-center p-4 bg-white/10 rounded-2xl backdrop-blur-md border border-white/20 transition-all duration-300 cursor-pointer hover:-translate-y-1 hover:bg-white/20"
          >
            <div className="flex justify-center mb-3 space-x-1">
              {stack.icons.map((IconComponent, iconIndex) => (
                <div
                  key={iconIndex}
                  className="text-2xl animate-float"
                  style={{
                    color: stack.colors[iconIndex],
                    animationDelay: `${iconIndex * 0.2}s`
                  }}
                >
                  <IconComponent />
                </div>
              ))}
            </div>
            <h6 className="text-white text-sm font-semibold mb-1">
              {stack.name}
            </h6>
            <p className="text-white/75 text-xs mb-0">
              {stack.description}
            </p>
          </div>
        ))}
      </div>
      
      {/* Scrolling Tech Banner */}
      <div className="scrolling-tech-banner mt-6 overflow-hidden">
        <div className="scrolling-content flex animate-scroll">
          {[...Array(3)].map((_, repeatIndex) => (
            <div key={repeatIndex} className="flex items-center">
              {techStacks.flatMap((stack, stackIndex) =>
                stack.icons.map((IconComponent, iconIndex) => (
                  <div
                    key={`${repeatIndex}-${stackIndex}-${iconIndex}`}
                    className="mx-6 text-3xl transition-all duration-300 hover:scale-110"
                    style={{
                      color: stack.colors[iconIndex] || 'rgba(255, 255, 255, 0.6)',
                      minWidth: '40px'
                    }}
                    title={`${stack.name} - ${IconComponent.name || 'Technology'}`}
                  >
                    <IconComponent />
                  </div>
                ))
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TechShowcase;
