<!DOCTYPE html>
<html>
<head>
    <title>Favicon Generator</title>
</head>
<body>
    <h2>Favicon Generator for Maruti IT Zone</h2>
    <p>To create proper favicon files from your logo.png:</p>
    
    <h3>Option 1: Online Tools (Recommended)</h3>
    <ol>
        <li>Go to <a href="https://favicon.io/favicon-converter/" target="_blank">favicon.io</a></li>
        <li>Upload your logo.png file</li>
        <li>Download the generated favicon package</li>
        <li>Replace the files in your public folder</li>
    </ol>
    
    <h3>Option 2: Manual Creation</h3>
    <ol>
        <li>Use image editing software to create these sizes from logo.png:</li>
        <ul>
            <li>favicon.ico (16x16, 32x32, 48x48)</li>
            <li>apple-touch-icon.png (180x180)</li>
            <li>android-chrome-192x192.png (192x192)</li>
            <li>android-chrome-512x512.png (512x512)</li>
        </ul>
    </ol>
    
    <h3>Current Setup</h3>
    <p>Your logo.png is now being used as:</p>
    <ul>
        <li>✅ Main favicon</li>
        <li>✅ Apple touch icon</li>
        <li>✅ Web app manifest icons</li>
        <li>✅ Open Graph image</li>
        <li>✅ Twitter card image</li>
        <li>✅ Structured data logo</li>
    </ul>
    
    <canvas id="canvas" width="32" height="32" style="border: 1px solid #ccc;"></canvas>
    <script>
        // Simple favicon preview
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();
        img.onload = function() {
            ctx.drawImage(img, 0, 0, 32, 32);
        };
        img.src = '/logo.png';
    </script>
</body>
</html>
