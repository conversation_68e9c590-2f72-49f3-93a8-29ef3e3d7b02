import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { COMPANY_INFO } from '../../assets/js/constants';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close menu when route changes
  useEffect(() => {
    setIsMenuOpen(false);
  }, [location]);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const isActive = (path) => {
    return location.pathname === path;
  };

  return (
    <>
      {/* Compact Top Bar */}
      <div className="top-bar bg-gradient-dark text-white d-none d-lg-block" style={{ padding: '4px 0' }}>
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-8">
              <div className="d-flex align-items-center gap-4">
                <small className="d-flex align-items-center">
                  <i className="fas fa-envelope me-1 text-warning"></i>
                  {COMPANY_INFO.email}
                </small>
                <small className="d-flex align-items-center">
                  <i className="fas fa-phone me-1 text-warning"></i>
                  {COMPANY_INFO.phone}
                </small>
                <small className="d-flex align-items-center">
                  <i className="fas fa-clock me-1 text-warning"></i>
                  Mon-Fri: 9AM-6PM
                </small>
              </div>
            </div>
            <div className="col-lg-4 text-end">
              <div className="d-flex align-items-center justify-content-end gap-2">
                <small className="me-2 opacity-75">Follow:</small>
                <a href="#" className="text-white hover-scale" style={{ fontSize: '0.85rem' }}>
                  <i className="fab fa-facebook-f"></i>
                </a>
                <a href="#" className="text-white hover-scale" style={{ fontSize: '0.85rem' }}>
                  <i className="fab fa-twitter"></i>
                </a>
                <a href="#" className="text-white hover-scale" style={{ fontSize: '0.85rem' }}>
                  <i className="fab fa-linkedin-in"></i>
                </a>
                <a href="#" className="text-white hover-scale" style={{ fontSize: '0.85rem' }}>
                  <i className="fab fa-instagram"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Menu Backdrop */}
      {isMenuOpen && (
        <div 
          className="mobile-backdrop"
          onClick={toggleMenu}
        />
      )}

      {/* Main Navigation */}
      <nav className={`navbar navbar-expand-lg navbar-light fixed-top main-navbar ${isScrolled ? 'navbar-scrolled' : ''}`}>
        <div className="container">
          {/* Compact Brand Logo */}
          <Link className="navbar-brand d-flex align-items-center py-1" to="/">
            <img
              src="/logo.png"
              alt="Maruti iT Zone"
              height="42"
              className="me-3"
            />
            <div className="brand-info">
              <div className="brand-name">
                <span className="brand-text fs-4 fw-bold text-gradient">Maruti iT Zone</span>
              </div>
              <div className="brand-tagline d-none d-lg-block" style={{ marginTop: '-2px' }}>
                <span className="text-muted" style={{ fontSize: '0.75rem', fontStyle: 'italic' }}>We Code Future...</span>
              </div>
            </div>
          </Link>

          {/* Modern Mobile Menu Toggle */}
          <button
            className="mobile-menu-toggle d-lg-none"
            type="button"
            onClick={toggleMenu}
            aria-label="Toggle navigation"
          >
            <div className={`hamburger ${isMenuOpen ? 'active' : ''}`}>
              <span></span>
              <span></span>
              <span></span>
            </div>
          </button>

          {/* Desktop Navigation */}
          <div className="d-none d-lg-flex align-items-center ms-auto">
            <ul className="navbar-nav me-4">
              <li className="nav-item">
                <Link className={`nav-link ${isActive('/') ? 'active' : ''}`} to="/">Home</Link>
              </li>
              <li className="nav-item">
                <Link className={`nav-link ${isActive('/about-us') ? 'active' : ''}`} to="/about-us">About</Link>
              </li>
              <li className="nav-item">
                <Link className={`nav-link ${location.pathname.startsWith('/services') ? 'active' : ''}`} to="/services">Services</Link>
              </li>
              <li className="nav-item">
                <Link className={`nav-link ${isActive('/portfolio') ? 'active' : ''}`} to="/portfolio">Portfolio</Link>
              </li>
              <li className="nav-item">
                <Link className={`nav-link ${isActive('/team') ? 'active' : ''}`} to="/team">Team</Link>
              </li>
              <li className="nav-item">
                <Link className={`nav-link ${isActive('/careers') ? 'active' : ''}`} to="/careers">Careers</Link>
              </li>
              <li className="nav-item">
                <Link className={`nav-link ${isActive('/contact-us') ? 'active' : ''}`} to="/contact-us">Contact</Link>
              </li>
            </ul>
            
            {/* Desktop Action Buttons */}
            <div className="d-flex gap-2">
              <Link to="/login" className="btn btn-brand-outline btn-sm px-3">
                <i className="fas fa-sign-in-alt me-1"></i>Login
              </Link>
              <Link to="/register" className="btn btn-brand-primary btn-sm px-3">
                <i className="fas fa-user-plus me-1"></i>Register
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Sidebar Menu */}
      <div className={`mobile-sidebar ${isMenuOpen ? 'active' : ''}`}>
        <div className="mobile-sidebar-content">
          {/* Mobile Header */}
          <div className="mobile-header">
            <div className="d-flex align-items-center">
              <img src="/logo.png" alt="Maruti iT Zone" height="32" className="me-2" />
              <div>
                <div className="fw-bold text-dark">Maruti iT Zone</div>
                <small className="text-muted fst-italic">We Code Future...</small>
              </div>
            </div>
            <button className="mobile-close-btn" onClick={toggleMenu}>
              <i className="fas fa-times"></i>
            </button>
          </div>

          {/* Mobile Navigation */}
          <nav className="mobile-nav">
            <Link className={`mobile-nav-link ${isActive('/') ? 'active' : ''}`} to="/" onClick={toggleMenu}>
              <i className="fas fa-home"></i>
              <span>Home</span>
            </Link>
            <Link className={`mobile-nav-link ${isActive('/about-us') ? 'active' : ''}`} to="/about-us" onClick={toggleMenu}>
              <i className="fas fa-info-circle"></i>
              <span>About-Us</span>
            </Link>

            <Link className={`mobile-nav-link ${location.pathname.startsWith('/services') ? 'active' : ''}`} to="/services" onClick={toggleMenu}>
              <i className="fas fa-cogs"></i>
              <span>Services</span>
            </Link>

            <Link className={`mobile-nav-link ${isActive('/portfolio') ? 'active' : ''}`} to="/portfolio" onClick={toggleMenu}>
              <i className="fas fa-briefcase"></i>
              <span>Portfolio</span>
            </Link>
            <Link className={`mobile-nav-link ${isActive('/team') ? 'active' : ''}`} to="/team" onClick={toggleMenu}>
              <i className="fas fa-users"></i>
              <span>Our-Team</span>
            </Link>
            <Link className={`mobile-nav-link ${isActive('/careers') ? 'active' : ''}`} to="/careers" onClick={toggleMenu}>
              <i className="fas fa-briefcase"></i>
              <span>Careers</span>
            </Link>
            <Link className={`mobile-nav-link ${isActive('/contact-us') ? 'active' : ''}`} to="/contact-us" onClick={toggleMenu}>
              <i className="fas fa-envelope"></i>
              <span>Contact-Us</span>
            </Link>
          </nav>

          {/* Mobile Action Buttons */}
          <div className="mobile-actions">
            <Link to="/login" className="mobile-btn mobile-btn-brand-outline" onClick={toggleMenu}>
              <i className="fas fa-sign-in-alt me-2"></i>Login
            </Link>
            <Link to="/register" className="mobile-btn mobile-btn-brand-primary" onClick={toggleMenu}>
              <i className="fas fa-user-plus me-2"></i>Register
            </Link>
          </div>

          {/* Mobile Contact Info */}
          <div className="mobile-contact">
            <div className="mobile-contact-item">
              <i className="fas fa-envelope text-primary"></i>
              <span>{COMPANY_INFO.email}</span>
            </div>
            <div className="mobile-contact-item">
              <i className="fas fa-phone text-primary"></i>
              <span>{COMPANY_INFO.phone}</span>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Header;
