import { Fa<PERSON>lock, FaMapMarkerAlt, FaEnvelope, FaPhone, FaFacebookF, FaTwitter, FaLinkedinIn, FaInstagram, FaYoutube } from 'react-icons/fa';
import { COMPANY_INFO } from '../../assets/js/constants';

const TopBar = () => {

  return (
    <div className="d-none d-lg-block py-2 position-fixed top-0 start-0 end-0 bg-light border-bottom text-sm" style={{ zIndex: 1040 }}>
      <div className="container">
        <div className="d-flex justify-content-between align-items-center">
          {/* Left Side - Contact Info */}
          <div className="d-flex align-items-center">
            <div className="d-flex align-items-center me-4">
              <FaClock className="me-2 text-muted" style={{ fontSize: '0.8rem' }} />
              <span className="text-muted small">
                Mon - Fri: 9:00 AM - 6:00 PM
              </span>
            </div>
            <div className="d-flex align-items-center">
              <FaMapMarkerAlt className="me-2 text-muted" style={{ fontSize: '0.8rem' }} />
              <span className="text-muted small">
                Aamar Talab Road, Dharmari, Raipur C.G.
              </span>
            </div>
          </div>

          {/* Right Side - Contact & Social */}
          <div className="d-flex align-items-center">
            {/* Contact Info */}
            <div className="d-flex align-items-center me-4">
              <a
                href={`mailto:${COMPANY_INFO.email}`}
                className="d-flex align-items-center text-decoration-none text-muted me-3"
                style={{ fontSize: '0.8rem' }}
              >
                <FaEnvelope className="me-1" />
                <span>{COMPANY_INFO.email}</span>
              </a>
              <a
                href={`tel:${COMPANY_INFO.phone.replace(/[^+\d]/g, '')}`}
                className="d-flex align-items-center text-decoration-none text-muted"
                style={{ fontSize: '0.8rem' }}
              >
                <FaPhone className="me-1" />
                <span>{COMPANY_INFO.phone}</span>
              </a>
            </div>

            {/* Social Media Links */}
            <div className="d-flex align-items-center">
              <a
                href="https://facebook.com/marutiitzone"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted me-2 p-1"
                aria-label="Facebook"
                style={{ fontSize: '0.8rem' }}
              >
                <FaFacebookF />
              </a>
              <a
                href="https://twitter.com/marutiitzone"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted me-2 p-1"
                aria-label="Twitter"
                style={{ fontSize: '0.8rem' }}
              >
                <FaTwitter />
              </a>
              <a
                href="https://linkedin.com/company/marutiitzone"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted me-2 p-1"
                aria-label="LinkedIn"
                style={{ fontSize: '0.8rem' }}
              >
                <FaLinkedinIn />
              </a>
              <a
                href="https://instagram.com/marutiitzone"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted me-2 p-1"
                aria-label="Instagram"
                style={{ fontSize: '0.8rem' }}
              >
                <FaInstagram />
              </a>
              <a
                href="https://youtube.com/marutiitzone"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted p-1"
                aria-label="YouTube"
                style={{ fontSize: '0.8rem' }}
              >
                <FaYoutube />
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopBar;
