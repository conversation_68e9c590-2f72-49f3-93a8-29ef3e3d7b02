import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

const HeroSection = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <section className="hero-gradient d-flex align-items-center mt-5 py-5 position-relative overflow-hidden">
      {/* Floating Programming Languages/Tools */}
      <div className="floating-elements">
        <div className="floating-item floating-item-1">
          <i className="fab fa-react"></i>
        </div>
        <div className="floating-item floating-item-2">
          <i className="fab fa-js-square"></i>
        </div>
        <div className="floating-item floating-item-3">
          <i className="fab fa-python"></i>
        </div>
        <div className="floating-item floating-item-4">
          <i className="fab fa-node-js"></i>
        </div>
        <div className="floating-item floating-item-5">
          <i className="fab fa-html5"></i>
        </div>
        <div className="floating-item floating-item-6">
          <i className="fab fa-css3-alt"></i>
        </div>
        <div className="floating-item floating-item-7">
          <i className="fab fa-angular"></i>
        </div>
        <div className="floating-item floating-item-8">
          <i className="fab fa-vue"></i>
        </div>
        <div className="floating-item floating-item-9">
          <i className="fab fa-php"></i>
        </div>
        <div className="floating-item floating-item-10">
          <i className="fab fa-laravel"></i>
        </div>
        <div className="floating-item floating-item-11">
          <i className="fab fa-docker"></i>
        </div>
        <div className="floating-item floating-item-12">
          <i className="fab fa-aws"></i>
        </div>
      </div>

      <div className="container position-relative">
        <div className="row align-items-center min-vh-100">
          {/* Left Content */}
          <div className="col-lg-6 col-md-12">
            <div className={`hero-content text-white ${isVisible ? 'animate-fade-in-up' : ''}`}>
              <h1 className="display-3 fw-bold mb-4">
                Transform <span className="text-warning">Your</span>Business with
                Innovative <span className="text-primary">IT Solutions</span>
              </h1>
              
              <p className="lead mb-4 opacity-90" style={{ fontSize: '1.2rem', lineHeight: '1.6' }}>
                Maruti iT Zone delivers cutting-edge technology solutions to help your business grow, 
                innovate, and stay ahead in the digital landscape.
              </p>

              <div className="d-flex flex-wrap gap-3 mb-5">
                <Link to="/services" className="btn btn-light btn-lg px-4 py-3 rounded-pill fw-semibold">
                  <i className="fas fa-cogs me-2"></i>
                  OUR SERVICES
                </Link>
                <Link to="/contact-us" className="btn btn-outline-light btn-lg px-4 py-3 rounded-pill fw-semibold">
                  <i className="fas fa-rocket me-2"></i>
                  GET STARTED
                </Link>
              </div>

              {/* Stats */}
              <div className="row g-4 mt-4">
                <div className="col-4">
                  <div className="text-center">
                    <h3 className="fw-bold mb-1">500+</h3>
                    <p className="small opacity-75 mb-0">Projects Completed</p>
                  </div>
                </div>
                <div className="col-4">
                  <div className="text-center">
                    <h3 className="fw-bold mb-1">150+</h3>
                    <p className="small opacity-75 mb-0">Happy Clients</p>
                  </div>
                </div>
                <div className="col-4">
                  <div className="text-center">
                    <h3 className="fw-bold mb-1">24/7</h3>
                    <p className="small opacity-75 mb-0">Support</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Content - AI Chakra with Programming Languages */}
          <div className="col-lg-6 col-md-12 position-relative">
            <div className="ai-chakra-container">
              {/* Central AI Avatar */}
              <div className="ai-avatar-wrapper">
                <div className="ai-avatar">
                  <div className="ai-face">
                    <i className="fas fa-robot text-primary"></i>
                  </div>
                  <div className="chakra-ring chakra-outer">
                    <div className="chakra-ring chakra-inner">
                      <div className="chakra-center"></div>
                    </div>
                  </div>
                </div>
                
                {/* Programming Language Icons - Spinning */}
                <div className="language-orbit orbit-1">
                  <div className="language-icon spinning" style={{'--delay': '0s'}}>
                    <i className="fab fa-js-square text-warning"></i>
                  </div>
                  <div className="language-icon spinning" style={{'--delay': '0.5s'}}>
                    <i className="fab fa-python text-info"></i>
                  </div>
                  <div className="language-icon spinning" style={{'--delay': '1s'}}>
                    <i className="fab fa-react text-cyan"></i>
                  </div>
                </div>
                
                <div className="language-orbit orbit-2">
                  <div className="language-icon spinning" style={{'--delay': '0.3s'}}>
                    <i className="fab fa-node-js text-success"></i>
                  </div>
                  <div className="language-icon spinning" style={{'--delay': '0.8s'}}>
                    <i className="fab fa-java text-danger"></i>
                  </div>
                  <div className="language-icon spinning" style={{'--delay': '1.3s'}}>
                    <i className="fab fa-docker text-primary"></i>
                  </div>
                </div>
                
                <div className="language-orbit orbit-3">
                  <div className="language-icon spinning" style={{'--delay': '0.2s'}}>
                    <i className="fab fa-aws text-orange"></i>
                  </div>
                  <div className="language-icon spinning" style={{'--delay': '0.7s'}}>
                    <i className="fab fa-git-alt text-dark"></i>
                  </div>
                  <div className="language-icon spinning" style={{'--delay': '1.2s'}}>
                    <i className="fab fa-linux text-secondary"></i>
                  </div>
                </div>
              </div>
              
              {/* Technology Stack Display */}
              <div className="tech-stack-display">
                <h5 className="text-center mb-3">
                  <i className="fas fa-cogs me-2"></i>
                  Powered by Modern Tech
                </h5>
                <div className="tech-grid">
                  <div className="tech-item">
                    <i className="fab fa-angular text-danger"></i>
                    <span>Angular</span>
                  </div>
                  <div className="tech-item">
                    <i className="fab fa-react text-info"></i>
                    <span>React</span>
                  </div>
                  <div className="tech-item">
                    <i className="fab fa-vuejs text-success"></i>
                    <span>Vue.js</span>
                  </div>
                  <div className="tech-item">
                    <i className="fab fa-laravel text-danger"></i>
                    <span>Laravel</span>
                  </div>
                  <div className="tech-item">
                    <i className="fab fa-python text-warning"></i>
                    <span>Python</span>
                  </div>
                  <div className="tech-item">
                    <i className="fab fa-php text-primary"></i>
                    <span>PHP</span>
                  </div>
                </div>
      </div>

      {/* Scroll Indicator */}
      <div className="scroll-indicator">
        <div className="scroll-arrow">
          <i className="fas fa-chevron-down text-white"></i>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
