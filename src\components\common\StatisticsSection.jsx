import { Container, Row, Col } from 'reactstrap';
import { FaProjectDiagram, FaUsers, FaTrophy, FaCode, FaRocket, FaAward } from 'react-icons/fa';
import AnimatedCounter from './AnimatedCounter';

const StatisticsSection = () => {

  const statistics = [
    {
      end: 150,
      suffix: '+',
      title: 'Successful Projects',
      description: 'Delivered with excellence',
      icon: FaProjectDiagram,
      color: 'var(--primary-orange)'
    },
    {
      end: 50,
      suffix: '+',
      title: 'Happy Clients',
      description: 'Satisfied worldwide',
      icon: FaUsers,
      color: 'var(--primary-blue)'
    },
    {
      end: 5,
      suffix: '+',
      title: 'Years of Excellence',
      description: 'Industry experience',
      icon: FaTrophy,
      color: 'var(--success)'
    },
    {
      end: 25,
      suffix: '+',
      title: 'Technologies',
      description: 'Modern tech stack',
      icon: FaCode,
      color: 'var(--info)'
    },
    {
      end: 100,
      suffix: '%',
      title: 'Client Satisfaction',
      description: 'Quality guaranteed',
      icon: FaAward,
      color: 'var(--warning)'
    },
    {
      end: 24,
      suffix: '/7',
      title: 'Support Available',
      description: 'Always here for you',
      icon: FaRocket,
      color: 'var(--danger)'
    }
  ];

  return (
    <section 
      className="statistics-section py-5"
      style={{
        background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%)',
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {/* Background Pattern */}
      <div 
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: 'radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.05) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(249, 115, 22, 0.05) 0%, transparent 50%)',
          pointerEvents: 'none'
        }}
      />

      <Container className="position-relative">
        {/* Section Header */}
        <Row className="mb-5">
          <Col lg={8} className="mx-auto text-center">
            <h2
              className="display-4 fw-bold mb-4"
              style={{
                background: 'linear-gradient(135deg, #1e293b 0%, #475569 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text'
              }}
            >
              Our Achievements in Numbers
            </h2>
            <p
              className="lead"
              style={{
                color: 'var(--gray)',
                fontSize: '1.2rem',
                lineHeight: '1.6'
              }}
            >
              We take pride in our track record of delivering exceptional results 
              and building lasting relationships with our clients worldwide.
            </p>
          </Col>
        </Row>

        {/* Statistics Grid */}
        <Row className="g-4">
          {statistics.map((stat, index) => (
            <Col lg={4} md={6} key={index}>
              <AnimatedCounter
                end={stat.end}
                suffix={stat.suffix}
                title={stat.title}
                description={stat.description}
                icon={stat.icon}
                color={stat.color}
                duration={2000 + (index * 200)} // Stagger animations
              />
            </Col>
          ))}
        </Row>

        {/* Call to Action */}
        <Row className="mt-5">
          <Col lg={8} className="mx-auto text-center">
            <div 
              className="p-4 rounded-4"
              style={{
                background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(249, 115, 22, 0.05) 100%)',
                border: '1px solid rgba(0, 0, 0, 0.1)',
                backdropFilter: 'blur(10px)'
              }}
            >
              <h4 
                className="mb-3"
                style={{
                  color: 'var(--default)',
                  fontWeight: '600'
                }}
              >
                Ready to Join Our Success Story?
              </h4>
              <p 
                className="mb-4"
                style={{
                  color: 'var(--gray)',
                  fontSize: '1.1rem'
                }}
              >
                Let's work together to bring your vision to life with our proven expertise 
                and commitment to excellence.
              </p>
              <div className="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                <button 
                  className="btn btn-lg px-4 py-3"
                  style={{
                    background: 'linear-gradient(135deg, var(--primary-orange) 0%, var(--primary-blue) 100%)',
                    border: 'none',
                    color: 'white',
                    fontWeight: '600',
                    borderRadius: '12px',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.transform = 'translateY(-2px)';
                    e.target.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.2)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.transform = 'translateY(0)';
                    e.target.style.boxShadow = 'none';
                  }}
                >
                  Start Your Project
                </button>
                <button 
                  className="btn btn-outline-primary btn-lg px-4 py-3"
                  style={{
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-dark)',
                    fontWeight: '600',
                    borderRadius: '12px',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.transform = 'translateY(-2px)';
                    e.target.style.backgroundColor = 'rgba(0, 0, 0, 0.05)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.transform = 'translateY(0)';
                    e.target.style.backgroundColor = 'transparent';
                  }}
                >
                  View Our Portfolio
                </button>
              </div>
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default StatisticsSection;
