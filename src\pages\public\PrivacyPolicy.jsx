import { Link } from 'react-router-dom';
import SEO from '../../components/common/SEO';
import { COMPANY_INFO } from '../../assets/js/constants';

const PrivacyPolicy = () => {
  return (
    <div className="privacy-policy-page">
      <SEO
        title="Privacy Policy - Maruti iT Zone | Data Protection & Privacy"
        description="Learn about how Maruti iT Zone protects your privacy and handles your personal data. Our comprehensive privacy policy explains our data collection and usage practices."
        keywords="privacy policy, data protection, personal information, cookies, GDPR, data security"
        url="https://marutiitzone.com/privacy-policy"
      />

      {/* Hero Section */}
      <section className="bg-gradient-primary py-5 text-white">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-8 mx-auto text-center">
              <div className="hero-icon mb-4">
                <i className="fas fa-shield-alt" style={{ fontSize: '4rem', color: 'rgba(255,255,255,0.9)' }}></i>
              </div>
              <h1 className="display-3 fw-bold text-white mb-4">
                Privacy Policy
              </h1>
              <p className="lead text-white mb-4 opacity-90">
                Your privacy is important to us. Learn how we protect and handle your personal information.
              </p>
              <div className="d-flex flex-wrap justify-content-center gap-3">
                <Link to="/" className="btn btn-light btn-lg rounded-pill px-4">
                  <i className="fas fa-home me-2"></i>
                  Back to Home
                </Link>
                <Link to="/contact" className="btn btn-outline-light btn-lg rounded-pill px-4">
                  <i className="fas fa-envelope me-2"></i>
                  Contact Us
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-5 bg-light">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-10">
              <div className="bg-white rounded-4 shadow-lg p-5">
                <div className="text-center mb-5">
                  <div className="d-inline-flex align-items-center justify-content-center bg-primary bg-opacity-10 rounded-circle mb-3" style={{ width: '80px', height: '80px' }}>
                    <i className="fas fa-shield-alt text-primary" style={{ fontSize: '2rem' }}></i>
                  </div>
                  <h2 className="h1 fw-bold mb-3 text-dark">
                    Privacy Policy
                  </h2>
                  <p className="lead text-muted">
                    Last updated: {new Date().toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>

                <div className="privacy-content">
                  {/* Section 1 */}
                  <div className="mb-5 p-4 rounded-3 bg-light">
                    <div className="d-flex align-items-center mb-3">
                      <div className="bg-primary bg-opacity-10 rounded-circle p-2 me-3">
                        <span className="text-primary fw-bold">1</span>
                      </div>
                      <h2 className="h3 fw-bold mb-0 text-dark">
                        Information We Collect
                      </h2>
                    </div>
                    <p className="mb-3 text-muted">
                      At Maruti iT Zone, we collect information you provide directly to us, such as when you:
                    </p>
                    <ul className="text-muted">
                      <li>Create an account or fill out forms on our website</li>
                      <li>Contact us for support or inquiries</li>
                      <li>Subscribe to our newsletter or marketing communications</li>
                      <li>Participate in surveys or feedback sessions</li>
                      <li>Use our services or interact with our website</li>
                    </ul>
                  </div>

                  {/* Section 2 */}
                  <div className="mb-5 p-4 rounded-3 bg-light">
                    <div className="d-flex align-items-center mb-3">
                      <div className="bg-primary bg-opacity-10 rounded-circle p-2 me-3">
                        <span className="text-primary fw-bold">2</span>
                      </div>
                      <h2 className="h3 fw-bold mb-0 text-dark">
                        How We Use Your Information
                      </h2>
                    </div>
                    <p className="mb-3 text-muted">
                      We use the information we collect to:
                    </p>
                    <ul className="text-muted">
                      <li>Provide, maintain, and improve our services</li>
                      <li>Process transactions and send related information</li>
                      <li>Send technical notices and support messages</li>
                      <li>Respond to your comments and questions</li>
                      <li>Communicate about products, services, and events</li>
                      <li>Monitor and analyze trends and usage</li>
                    </ul>
                  </div>

                  {/* Section 3 */}
                  <div className="mb-5 p-4 rounded-3 bg-light">
                    <div className="d-flex align-items-center mb-3">
                      <div className="bg-primary bg-opacity-10 rounded-circle p-2 me-3">
                        <span className="text-primary fw-bold">3</span>
                      </div>
                      <h2 className="h3 fw-bold mb-0 text-dark">
                        Information Sharing
                      </h2>
                    </div>
                    <p className="text-muted">
                      We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy. We may share your information in the following circumstances:
                    </p>
                    <ul className="text-muted">
                      <li>With service providers who assist us in operating our website</li>
                      <li>To comply with legal obligations or protect our rights</li>
                      <li>In connection with a business transfer or acquisition</li>
                      <li>With your explicit consent</li>
                    </ul>
                  </div>

                  {/* Section 4 */}
                  <div className="mb-5 p-4 rounded-3 bg-light">
                    <div className="d-flex align-items-center mb-3">
                      <div className="bg-primary bg-opacity-10 rounded-circle p-2 me-3">
                        <span className="text-primary fw-bold">4</span>
                      </div>
                      <h2 className="h3 fw-bold mb-0 text-dark">
                        Data Security
                      </h2>
                    </div>
                    <p className="text-muted">
                      We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the internet is 100% secure.
                    </p>
                  </div>

                  {/* Section 5 */}
                  <div className="mb-5 p-4 rounded-3 bg-light">
                    <div className="d-flex align-items-center mb-3">
                      <div className="bg-primary bg-opacity-10 rounded-circle p-2 me-3">
                        <span className="text-primary fw-bold">5</span>
                      </div>
                      <h2 className="h3 fw-bold mb-0 text-dark">
                        Your Rights
                      </h2>
                    </div>
                    <p className="text-muted">
                      You have the right to access, update, or delete your personal information. You may also opt out of certain communications from us. To exercise these rights, please contact us using the information provided below.
                    </p>
                  </div>

                  {/* Contact Information */}
                  <div className="text-center mt-5 p-4 rounded-3 bg-primary bg-opacity-10">
                    <h3 className="fw-bold mb-4 text-dark">Questions About This Policy?</h3>
                    <p className="text-muted mb-4">
                      If you have any questions about this Privacy Policy, please contact us:
                    </p>
                    <div className="d-flex flex-wrap justify-content-center gap-4">
                      <div className="d-flex align-items-center">
                        <i className="fas fa-envelope text-primary me-2"></i>
                        <span className="text-dark">{COMPANY_INFO.email}</span>
                      </div>
                      <div className="d-flex align-items-center">
                        <i className="fas fa-phone text-primary me-2"></i>
                        <span className="text-dark">{COMPANY_INFO.phone}</span>
                      </div>
                      <div className="d-flex align-items-center">
                        <i className="fas fa-map-marker-alt text-primary me-2"></i>
                        <span className="text-dark">{COMPANY_INFO.address.city}, {COMPANY_INFO.address.state}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-primary py-5 text-center text-white">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-8">
              <h2 className="display-4 fw-bold mb-4">
                Ready to Work With Us?
              </h2>
              <p className="lead mb-4 opacity-90">
                We're committed to protecting your privacy while delivering exceptional IT solutions.
              </p>
              <div className="d-flex flex-wrap justify-content-center gap-3">
                <Link
                  to="/contact-us"
                  className="btn btn-light btn-lg rounded-pill px-4"
                >
                  <i className="fas fa-rocket me-2"></i>
                  Get Started Today
                </Link>
                <Link
                  to="/services"
                  className="btn btn-outline-light btn-lg rounded-pill px-4"
                >
                  <i className="fas fa-cogs me-2"></i>
                  Our Services
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PrivacyPolicy;
