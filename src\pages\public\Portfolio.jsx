import { useState } from 'react';
import { Link } from 'react-router-dom';

const Portfolio = () => {
  const [activeFilter, setActiveFilter] = useState('all');

  const projects = [
    {
      id: 1,
      title: 'E-Commerce Platform',
      category: 'web',
      image: 'https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=400&h=250&fit=crop',
      description: 'Modern e-commerce platform with payment integration, inventory management, and advanced analytics dashboard.',
      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe', 'AWS'],
      client: 'RetailCorp',
      duration: '4 months',
      liveUrl: '#',
      caseStudyUrl: '#'
    },
    {
      id: 2,
      title: 'Healthcare Mobile App',
      category: 'mobile',
      image: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=250&fit=crop',
      description: 'Patient management system with appointment booking, telemedicine features, and health tracking.',
      technologies: ['React Native', 'Firebase', 'Node.js', 'Socket.io'],
      client: 'MediCare Solutions',
      duration: '6 months',
      liveUrl: '#',
      caseStudyUrl: '#'
    },
    {
      id: 3,
      title: 'Cloud Migration Project',
      category: 'cloud',
      image: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=400&h=250&fit=crop',
      description: 'Complete infrastructure migration to AWS with improved scalability, security, and cost optimization.',
      technologies: ['AWS', 'Docker', 'Kubernetes', 'Terraform', 'Jenkins'],
      client: 'TechStart Inc',
      duration: '3 months',
      liveUrl: '#',
      caseStudyUrl: '#'
    },
    {
      id: 4,
      title: 'Digital Marketing Campaign',
      category: 'marketing',
      image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=250&fit=crop',
      description: 'Comprehensive digital marketing strategy resulting in 300% increase in leads and improved ROI.',
      technologies: ['Google Ads', 'Facebook Ads', 'Analytics', 'SEO', 'Content Marketing'],
      client: 'GrowthCo',
      duration: '6 months',
      liveUrl: '#',
      caseStudyUrl: '#'
    },
    {
      id: 5,
      title: 'Restaurant Management System',
      category: 'web',
      image: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=400&h=250&fit=crop',
      description: 'Complete restaurant management solution with POS, inventory tracking, and customer management.',
      technologies: ['Vue.js', 'Laravel', 'MySQL', 'Payment Gateway', 'PWA'],
      client: 'FoodChain Ltd',
      duration: '5 months',
      liveUrl: '#',
      caseStudyUrl: '#'
    },
    {
      id: 6,
      title: 'Fitness Tracking App',
      category: 'mobile',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=250&fit=crop',
      description: 'Cross-platform fitness app with workout tracking, social features, and AI-powered recommendations.',
      technologies: ['Flutter', 'Firebase', 'Machine Learning', 'TensorFlow'],
      client: 'FitLife',
      duration: '4 months',
      liveUrl: '#',
      caseStudyUrl: '#'
    }
  ];

  const categories = [
    { key: 'all', label: 'All Projects' },
    { key: 'web', label: 'Web Development' },
    { key: 'mobile', label: 'Mobile Apps' },
    { key: 'cloud', label: 'Cloud Solutions' },
    { key: 'marketing', label: 'Digital Marketing' }
  ];

  const filteredProjects = activeFilter === 'all'
    ? projects
    : projects.filter(project => project.category === activeFilter);

  return (
    <div className="portfolio-page">
      {/* Hero Section */}
      <section className="bg-gradient-primary py-5 text-white text-center">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-8">
              <h1 className="display-3 fw-bold mb-4">Our Portfolio</h1>
              <p className="lead mb-4 opacity-90">
                Showcasing our successful projects and the impact we've made for our clients
                across various industries and technologies.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Filter Navigation */}
      <section className="py-4 bg-light">
        <div className="container">
          <div className="row">
            <div className="col">
              <div className="d-flex justify-content-center flex-wrap gap-2">
                {categories.map(category => (
                  <button
                    key={category.key}
                    className={`btn ${activeFilter === category.key ? 'btn-brand-primary' : 'btn-brand-outline'} rounded-pill`}
                    onClick={() => setActiveFilter(category.key)}
                  >
                    {category.label}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-5">
        <div className="container">
          <div className="row g-4">
            {filteredProjects.map(project => (
              <div key={project.id} className="col-lg-4 col-md-6">
                <div className="card-modern h-100 overflow-hidden">
                  {/* Project Image */}
                  <div className="position-relative">
                    <img
                      src={project.image}
                      alt={project.title}
                      className="w-100"
                      style={{ height: '250px', objectFit: 'cover' }}
                    />
                    <div className="position-absolute top-0 end-0 m-3">
                      <span className="badge bg-gradient-primary px-3 py-2">
                        {categories.find(cat => cat.key === project.category)?.label}
                      </span>
                    </div>
                  </div>

                  {/* Project Content */}
                  <div className="p-4">
                    <h5 className="fw-bold mb-3">{project.title}</h5>
                    <p className="text-muted mb-3">{project.description}</p>

                    {/* Client & Duration */}
                    <div className="row mb-3">
                      <div className="col-6">
                        <small className="text-muted d-block">Client</small>
                        <strong className="text-gradient">{project.client}</strong>
                      </div>
                      <div className="col-6">
                        <small className="text-muted d-block">Duration</small>
                        <strong>{project.duration}</strong>
                      </div>
                    </div>

                    {/* Technologies */}
                    <div className="mb-4">
                      <small className="text-muted d-block mb-2">Technologies Used</small>
                      <div className="d-flex flex-wrap gap-1">
                        {project.technologies.map((tech, idx) => (
                          <span
                            key={idx}
                            className="badge bg-light text-dark border"
                            style={{ fontSize: '0.75rem' }}
                          >
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="d-flex gap-2">
                      <Link
                        to={project.liveUrl}
                        className="btn btn-brand-primary btn-sm flex-fill"
                      >
                        <i className="fas fa-external-link-alt me-1"></i>
                        Live Preview
                      </Link>
                      <Link
                        to={project.caseStudyUrl}
                        className="btn btn-brand-outline btn-sm flex-fill"
                      >
                        <i className="fas fa-file-alt me-1"></i>
                        Case Study
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-5 bg-light">
        <div className="container">
          <div className="row text-center">
            <div className="col-lg-3 col-md-6 mb-4">
              <div className="stat-item">
                <h2 className="display-4 fw-bold mb-2 text-gradient">150+</h2>
                <p className="lead">Projects Completed</p>
              </div>
            </div>
            <div className="col-lg-3 col-md-6 mb-4">
              <div className="stat-item">
                <h2 className="display-4 fw-bold mb-2 text-gradient">50+</h2>
                <p className="lead">Happy Clients</p>
              </div>
            </div>
            <div className="col-lg-3 col-md-6 mb-4">
              <div className="stat-item">
                <h2 className="display-4 fw-bold mb-2 text-gradient">98%</h2>
                <p className="lead">Success Rate</p>
              </div>
            </div>
            <div className="col-lg-3 col-md-6 mb-4">
              <div className="stat-item">
                <h2 className="display-4 fw-bold mb-2 text-gradient">24/7</h2>
                <p className="lead">Support Available</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-5">
        <div className="container">
          <div className="row justify-content-center mb-5">
            <div className="col-lg-8 text-center">
              <h2 className="display-4 fw-bold mb-4">What Our <span className="text-gradient">Clients Say</span></h2>
              <p className="lead text-muted">Real feedback from our satisfied clients</p>
            </div>
          </div>
          <div className="row g-4">
            <div className="col-lg-4">
              <div className="card-modern h-100 text-center p-4">
                <div className="mb-3">
                  <i className="fas fa-star text-warning"></i>
                  <i className="fas fa-star text-warning"></i>
                  <i className="fas fa-star text-warning"></i>
                  <i className="fas fa-star text-warning"></i>
                  <i className="fas fa-star text-warning"></i>
                </div>
                <p className="mb-3 fst-italic">
                  "Maruti iT Zone delivered an exceptional e-commerce platform that exceeded our expectations.
                  Their team's expertise and dedication were outstanding."
                </p>
                <h6 className="mb-1">John Smith</h6>
                <small className="text-muted">CEO, RetailCorp</small>
              </div>
            </div>
            <div className="col-lg-4">
              <div className="card-modern h-100 text-center p-4">
                <div className="mb-3">
                  <i className="fas fa-star text-warning"></i>
                  <i className="fas fa-star text-warning"></i>
                  <i className="fas fa-star text-warning"></i>
                  <i className="fas fa-star text-warning"></i>
                  <i className="fas fa-star text-warning"></i>
                </div>
                <p className="mb-3 fst-italic">
                  "The mobile app they developed for our healthcare practice has revolutionized
                  how we interact with patients. Highly recommended!"
                </p>
                <h6 className="mb-1">Dr. Sarah Johnson</h6>
                <small className="text-muted">Director, MediCare Solutions</small>
              </div>
            </div>
            <div className="col-lg-4">
              <div className="card-modern h-100 text-center p-4">
                <div className="mb-3">
                  <i className="fas fa-star text-warning"></i>
                  <i className="fas fa-star text-warning"></i>
                  <i className="fas fa-star text-warning"></i>
                  <i className="fas fa-star text-warning"></i>
                  <i className="fas fa-star text-warning"></i>
                </div>
                <p className="mb-3 fst-italic">
                  "Their cloud migration services helped us reduce costs by 40% while improving
                  performance. Professional and reliable team."
                </p>
                <h6 className="mb-1">Mike Chen</h6>
                <small className="text-muted">CTO, TechStart Inc</small>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-primary py-5 text-white text-center">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-8">
              <h2 className="display-4 fw-bold mb-4">Ready to Start Your Project?</h2>
              <p className="lead mb-4 opacity-90">
                Join our list of satisfied clients and let us help you achieve your business goals.
              </p>
              <Link to="/contact" className="btn btn-light btn-lg rounded-pill px-4">
                <i className="fas fa-rocket me-2"></i>
                Start Your Project
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Portfolio;
