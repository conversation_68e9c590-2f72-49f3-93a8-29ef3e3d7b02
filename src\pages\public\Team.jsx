
import { Link } from 'react-router-dom';
import SEO from '../../components/common/SEO';

const Team = () => {
  const leadership = [
    {
      name: '<PERSON><PERSON>',
      position: 'CEO & Founder',
      image: '👨‍💼',
      description: 'Visionary leader with 15+ years of experience in IT industry. Passionate about driving innovation and business growth.',
      skills: ['Strategic Planning', 'Business Development', 'Team Leadership', 'Client Relations'],
      social: { linkedin: '#', twitter: '#', email: 'r<PERSON><PERSON>@marutiitzone.com' }
    },
    {
      name: '<PERSON><PERSON>',
      position: 'Chief Technology Officer',
      image: '👩‍💻',
      description: 'Technology expert with deep knowledge in software architecture and emerging technologies. Leads our technical vision.',
      skills: ['Software Architecture', 'Cloud Computing', 'AI/ML', 'DevOps'],
      social: { linkedin: '#', twitter: '#', email: '<EMAIL>' }
    },
    {
      name: 'Am<PERSON> <PERSON>',
      position: 'Head of Operations',
      image: '👨‍💼',
      description: 'Operations specialist ensuring smooth project delivery and client satisfaction. Expert in process optimization.',
      skills: ['Project Management', 'Quality Assurance', 'Process Optimization', 'Client Success'],
      social: { linkedin: '#', twitter: '#', email: '<EMAIL>' }
    }
  ];

  const developers = [
    {
      name: 'Sneha <PERSON>',
      position: 'Senior Full Stack Developer',
      image: '👩‍💻',
      specialization: 'React, Node.js, MongoDB',
      experience: '6+ years'
    },
    {
      name: 'Rohit Singh',
      position: 'Mobile App Developer',
      image: '👨‍💻',
      specialization: 'React Native, Flutter',
      experience: '5+ years'
    },
    {
      name: 'Kavya Reddy',
      position: 'UI/UX Designer',
      image: '👩‍🎨',
      specialization: 'Design Systems, Prototyping',
      experience: '4+ years'
    },
    {
      name: 'Arjun Mehta',
      position: 'DevOps Engineer',
      image: '👨‍💻',
      specialization: 'AWS, Docker, Kubernetes',
      experience: '5+ years'
    },
    {
      name: 'Pooja Jain',
      position: 'Digital Marketing Specialist',
      image: '👩‍💼',
      specialization: 'SEO, SEM, Social Media',
      experience: '4+ years'
    },
    {
      name: 'Vikram Rao',
      position: 'Cybersecurity Analyst',
      image: '👨‍💻',
      specialization: 'Security Audits, Penetration Testing',
      experience: '6+ years'
    }
  ];

  const values = [
    {
      icon: '🎯',
      title: 'Excellence',
      description: 'We strive for excellence in every project and continuously improve our skills.'
    },
    {
      icon: '🤝',
      title: 'Collaboration',
      description: 'We believe in teamwork and open communication to achieve the best results.'
    },
    {
      icon: '💡',
      title: 'Innovation',
      description: 'We embrace new technologies and creative solutions to solve complex problems.'
    },
    {
      icon: '🌟',
      title: 'Growth',
      description: 'We are committed to continuous learning and professional development.'
    }
  ];

  return (
    <div className="team-page">
      <SEO
        title="Our Team - Maruti iT Zone | Meet Our Expert Professionals"
        description="Meet the talented team at Maruti iT Zone. Our experienced professionals are dedicated to delivering exceptional IT solutions and services."
        keywords="Maruti iT Zone team, IT professionals, web developers, mobile app developers, cloud experts"
        url="https://marutiitzone.com/team"
      />

      {/* Hero Section */}
      <section className="bg-gradient-primary py-5 text-white text-center">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-8">
              <h1 className="display-3 fw-bold mb-4">Meet Our <span className="text-warning">Team</span></h1>
              <p className="lead mb-4 opacity-90">
                Passionate professionals dedicated to delivering exceptional IT solutions
                and driving your business success.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Leadership Team */}
      <section className="py-5">
        <div className="container">
          <div className="text-center mb-5">
            <h2 className="display-5 fw-bold mb-4">Leadership Team</h2>
            <p className="lead text-muted">Experienced leaders guiding our vision and strategy</p>
          </div>
          <div className="row">
            {leadership.map((leader, index) => (
              <div className="col-lg-4 col-md-6 mb-4" key={index}>
                <div className="card h-100 shadow-sm border-0 rounded-4">
                  <div className="card-body p-4 text-center">
                    <div 
                      style={{
                        fontSize: '4rem',
                        width: '100px',
                        height: '100px',
                        background: 'var(--gradient-primary)',
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        margin: '0 auto 1rem',
                        color: 'white'
                      }}
                    >
                      {leader.image}
                    </div>
                    <h5 className="mb-1">{leader.name}</h5>
                    <p className="text-primary mb-3">{leader.position}</p>
                    <p className="text-muted mb-3">{leader.description}</p>
                    
                    <div className="mb-3">
                      <h6 className="mb-2">Expertise:</h6>
                      <div className="d-flex flex-wrap justify-content-center">
                        {leader.skills.map((skill, idx) => (
                          <span 
                            key={idx}
                            className="badge me-1 mb-1"
                            style={{ 
                              background: 'var(--light-gray)',
                              color: 'var(--text-dark)',
                              fontSize: '0.7rem'
                            }}
                          >
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                    
                    <div className="d-flex justify-content-center">
                      <a href={leader.social.linkedin} className="me-2" style={{ fontSize: '1.2rem' }}>💼</a>
                      <a href={leader.social.twitter} className="me-2" style={{ fontSize: '1.2rem' }}>🐦</a>
                      <a href={`mailto:${leader.social.email}`} style={{ fontSize: '1.2rem' }}>📧</a>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Development Team */}
      <section className="py-5 bg-light">
        <div className="container">
          <div className="text-center mb-5">
            <h2 className="display-5 fw-bold mb-4">Our Development Team</h2>
            <p className="lead text-muted">Skilled professionals bringing your ideas to life</p>
          </div>
          <div className="row">
            {developers.map((developer, index) => (
              <div className="col-lg-4 col-md-6 mb-4" key={index}>
                <div className="card h-100 shadow-sm border-0 rounded-4">
                  <div className="card-body p-4 text-center">
                    <div 
                      style={{
                        fontSize: '3rem',
                        width: '80px',
                        height: '80px',
                        background: 'var(--gradient-secondary)',
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        margin: '0 auto 1rem',
                        color: 'white'
                      }}
                    >
                      {developer.image}
                    </div>
                    <h6 className="mb-1">{developer.name}</h6>
                    <p className="text-primary mb-2 small">{developer.position}</p>
                    <p className="text-muted mb-2 small">
                      <strong>Specialization:</strong> {developer.specialization}
                    </p>
                    <p className="text-muted small">
                      <strong>Experience:</strong> {developer.experience}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Values */}
      <section className="py-5">
        <div className="container">
          <div className="text-center mb-5">
            <h2 className="display-5 fw-bold mb-4">Our Team Values</h2>
            <p className="lead text-muted">The principles that unite us and drive our success</p>
          </div>
          <div className="row">
            {values.map((value, index) => (
              <div className="col-lg-3 col-md-6 mb-4" key={index}>
                <div className="card h-100 text-center shadow-sm border-0 rounded-4">
                  <div className="card-body p-4">
                    <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>
                      {value.icon}
                    </div>
                    <h5 className="mb-3 fw-bold">{value.title}</h5>
                    <p className="text-muted">{value.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Join Our Team */}
      <section className="bg-gradient-primary py-5">
        <div className="container">
          <div className="row">
            <div className="col-lg-8 mx-auto text-center text-white">
              <h2 className="display-4 fw-bold mb-4">Join Our Team</h2>
              <p className="lead mb-4">
                We're always looking for talented individuals who share our passion for technology
                and innovation. Be part of our growing team!
              </p>
              <div className="mb-4">
                <h5 className="mb-3">Current Openings:</h5>
                <ul className="list-unstyled">
                  <li className="mb-2">• Senior React Developer</li>
                  <li className="mb-2">• Python Backend Developer</li>
                  <li className="mb-2">• UI/UX Designer</li>
                  <li className="mb-2">• Digital Marketing Specialist</li>
                </ul>
              </div>
              <Link
                to="/careers"
                className="btn btn-outline-light btn-lg rounded-pill px-4"
              >
                <i className="fas fa-briefcase me-2"></i>
                View All Openings
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Team Stats */}
      <section className="py-5 bg-light">
        <div className="container">
          <div className="row text-center">
            <div className="col-lg-3 col-md-6 mb-4">
              <div className="text-center">
                <h2 className="display-4 fw-bold mb-2 text-primary">15+</h2>
                <p className="lead text-muted">Team Members</p>
              </div>
            </div>
            <div className="col-lg-3 col-md-6 mb-4">
              <div className="text-center">
                <h2 className="display-4 fw-bold mb-2 text-primary">50+</h2>
                <p className="lead text-muted">Years Combined Experience</p>
              </div>
            </div>
            <div className="col-lg-3 col-md-6 mb-4">
              <div className="text-center">
                <h2 className="display-4 fw-bold mb-2 text-primary">20+</h2>
                <p className="lead text-muted">Technologies Mastered</p>
              </div>
            </div>
            <div className="col-lg-3 col-md-6 mb-4">
              <div className="text-center">
                <h2 className="display-4 fw-bold mb-2 text-primary">100%</h2>
                <p className="lead text-muted">Commitment to Excellence</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Team;
