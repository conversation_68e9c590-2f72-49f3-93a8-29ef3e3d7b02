import { Navbar, Nav<PERSON><PERSON><PERSON>, Button, Container } from 'reactstrap';
import { useAuth } from '../../context/AuthContext';

const AuthorizedHeader = ({ toggleSidebar }) => {
  const { user } = useAuth();

  return (
    <Navbar
      className="authorized-header shadow-sm"
      style={{
        marginLeft: window.innerWidth >= 768 ? '250px' : '0',
        transition: 'margin-left 0.3s ease',
        background: 'white',
        color: 'var(--default)'
      }}
    >
      <Container fluid>
        <div className="d-flex align-items-center">
          {/* Mobile Menu Toggle */}
          <Button
            color="link"
            className="d-md-none me-3 p-0"
            onClick={toggleSidebar}
            style={{
              fontSize: '1.5rem',
              color: 'var(--default)'
            }}
          >
            ☰
          </Button>

          <NavbarBrand
            className="mb-0 h1"
            style={{
              color: 'var(--default)'
            }}
          >
            Authorized Dashboard
          </NavbarBrand>
        </div>

        {/* User Info */}
        <div className="d-flex align-items-center">
          <div className="me-3 text-end d-none d-sm-block">
            <div
              className="fw-bold"
              style={{
                color: 'var(--default)'
              }}
            >
              {user?.name || 'Authorized User'}
            </div>
            <small
              style={{
                color: 'var(--gray)'
              }}
            >
              {user?.email || '<EMAIL>'}
            </small>
          </div>
          
          <div 
            style={{
              width: '40px',
              height: '40px',
              background: 'linear-gradient(135deg, #FF5722 0%, #2196F3 100%)',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: 'bold',
              fontSize: '1.1rem'
            }}
          >
            {user?.name ? user.name.charAt(0).toUpperCase() : 'A'}
          </div>
        </div>
      </Container>
    </Navbar>
  );
};

export default AuthorizedHeader;
