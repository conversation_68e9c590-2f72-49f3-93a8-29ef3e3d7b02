
import { Helmet } from 'react-helmet-async';
import { COMPANY_INFO } from '../../assets/js/constants';

const SEO = ({ 
  title = 'Maruti IT Zone - Leading IT Solutions Provider',
  description = 'Transform your business with innovative IT solutions. We specialize in web development, mobile apps, cloud services, and digital marketing.',
  keywords = 'IT solutions, web development, mobile app development, digital marketing, cloud services, Maruti IT Zone',
  image = 'https://marutiitzone.com/logo.png',
  url = 'https://marutiitzone.com',
  type = 'website',
  author = 'Maruti IT Zone',
  canonical,
  noindex = false,
  nofollow = false
}) => {
  const fullTitle = title.includes('Maruti IT Zone') ? title : `${title} | Maruti IT Zone`;
  const canonicalUrl = canonical || url;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content={author} />
      
      {/* Robots Meta Tags */}
      <meta name="robots" content={`${noindex ? 'noindex' : 'index'}, ${nofollow ? 'nofollow' : 'follow'}`} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={canonicalUrl} />
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={type} />
      <meta property="og:url" content={url} />
      <meta property="og:image" content={image} />
      <meta property="og:site_name" content="Maruti IT Zone" />
      <meta property="og:locale" content="en_US" />
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      <meta name="twitter:site" content="@marutiitzone" />
      <meta name="twitter:creator" content="@marutiitzone" />
      
      {/* Additional Meta Tags */}
      <meta name="theme-color" content="#FF5722" />
      <meta name="msapplication-TileColor" content="#FF5722" />
      
      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": COMPANY_INFO.name,
          "url": COMPANY_INFO.website,
          "logo": `${COMPANY_INFO.website}/logo.png`,
          "description": description,
          "address": {
            "@type": "PostalAddress",
            "streetAddress": COMPANY_INFO.address.street,
            "addressLocality": COMPANY_INFO.address.city,
            "addressRegion": COMPANY_INFO.address.state,
            "postalCode": COMPANY_INFO.address.zipCode,
            "addressCountry": "IN"
          },
          "contactPoint": {
            "@type": "ContactPoint",
            "telephone": COMPANY_INFO.phone,
            "contactType": "customer service",
            "email": COMPANY_INFO.email
          },
          "sameAs": [
            "https://facebook.com/marutiitzone",
            "https://twitter.com/marutiitzone",
            "https://linkedin.com/company/marutiitzone",
            "https://instagram.com/marutiitzone"
          ],
          "foundingDate": "2019",
          "numberOfEmployees": "15-50",
          "industry": "Information Technology"
        })}
      </script>
    </Helmet>
  );
};

export default SEO;
