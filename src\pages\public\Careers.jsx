import { useState } from 'react';
import { Link } from 'react-router-dom';
import SEO from '../../components/common/SEO';
import { COMPANY_INFO } from '../../assets/js/constants';

const Careers = () => {
  const [activeTab, setActiveTab] = useState('jobs');

  const jobOpenings = [
    {
      id: 1,
      title: 'Senior Full Stack Developer',
      department: 'Development',
      location: `${COMPANY_INFO.address.city}, ${COMPANY_INFO.address.state}`,
      type: 'Full-time',
      experience: '3-5 years',
      salary: '₹8,00,000 - ₹12,00,000',
      skills: ['React', 'Node.js', 'MongoDB', 'Express.js', 'TypeScript'],
      description: 'We are looking for an experienced Full Stack Developer to join our dynamic team. You will be responsible for developing and maintaining web applications using modern technologies.',
      requirements: [
        'Bachelor\'s degree in Computer Science or related field',
        '3+ years of experience in full-stack development',
        'Proficiency in React, Node.js, and MongoDB',
        'Experience with RESTful APIs and microservices',
        'Strong problem-solving skills'
      ]
    },
    {
      id: 2,
      title: 'UI/UX Designer',
      department: 'Design',
      location: `${COMPANY_INFO.address.city}, ${COMPANY_INFO.address.state}`,
      type: 'Full-time',
      experience: '2-4 years',
      salary: '₹5,00,000 - ₹8,00,000',
      skills: ['Figma', 'Adobe XD', 'Photoshop', 'Illustrator', 'Prototyping'],
      description: 'Join our creative team as a UI/UX Designer and help create amazing user experiences for our clients. You will work on diverse projects ranging from web applications to mobile apps.',
      requirements: [
        'Bachelor\'s degree in Design or related field',
        '2+ years of UI/UX design experience',
        'Proficiency in design tools like Figma, Adobe XD',
        'Strong portfolio showcasing design projects',
        'Understanding of user-centered design principles'
      ]
    },
    {
      id: 3,
      title: 'Digital Marketing Specialist',
      department: 'Marketing',
      location: `${COMPANY_INFO.address.city}, ${COMPANY_INFO.address.state}`,
      type: 'Full-time',
      experience: '1-3 years',
      salary: '₹3,50,000 - ₹6,00,000',
      skills: ['SEO', 'Google Ads', 'Social Media', 'Content Marketing', 'Analytics'],
      description: 'We are seeking a Digital Marketing Specialist to develop and execute digital marketing strategies that drive brand awareness and lead generation.',
      requirements: [
        'Bachelor\'s degree in Marketing or related field',
        '1+ years of digital marketing experience',
        'Knowledge of SEO, SEM, and social media marketing',
        'Experience with Google Analytics and Google Ads',
        'Excellent communication and analytical skills'
      ]
    }
  ];

  const internships = [
    {
      id: 1,
      title: 'Frontend Development Intern',
      department: 'Development',
      location: `${COMPANY_INFO.address.city}, ${COMPANY_INFO.address.state}`,
      duration: '3-6 months',
      stipend: '₹15,000 - ₹25,000/month',
      skills: ['HTML', 'CSS', 'JavaScript', 'React', 'Git'],
      description: 'Learn and work with modern frontend technologies while contributing to real projects. Perfect opportunity for students and fresh graduates.',
      requirements: [
        'Currently pursuing or recently completed degree in Computer Science',
        'Basic knowledge of HTML, CSS, and JavaScript',
        'Familiarity with React is a plus',
        'Eager to learn and grow',
        'Good communication skills'
      ]
    },
    {
      id: 2,
      title: 'Backend Development Intern',
      department: 'Development',
      location: `${COMPANY_INFO.address.city}, ${COMPANY_INFO.address.state}`,
      duration: '3-6 months',
      stipend: '₹15,000 - ₹25,000/month',
      skills: ['Node.js', 'Express.js', 'MongoDB', 'APIs', 'Git'],
      description: 'Gain hands-on experience in backend development and database management while working on live projects.',
      requirements: [
        'Currently pursuing or recently completed degree in Computer Science',
        'Basic knowledge of programming concepts',
        'Familiarity with Node.js or Python is a plus',
        'Understanding of databases',
        'Problem-solving mindset'
      ]
    },
    {
      id: 3,
      title: 'Digital Marketing Intern',
      department: 'Marketing',
      location: `${COMPANY_INFO.address.city}, ${COMPANY_INFO.address.state}`,
      duration: '3-6 months',
      stipend: '₹10,000 - ₹18,000/month',
      skills: ['Social Media', 'Content Writing', 'SEO Basics', 'Analytics', 'Canva'],
      description: 'Learn digital marketing strategies and tools while assisting with real marketing campaigns and content creation.',
      requirements: [
        'Currently pursuing degree in Marketing, Communications, or related field',
        'Basic understanding of social media platforms',
        'Good writing and communication skills',
        'Creative thinking and attention to detail',
        'Willingness to learn new tools and techniques'
      ]
    },
    {
      id: 4,
      title: 'Graphic Design Intern (Unpaid)',
      department: 'Design',
      location: `${COMPANY_INFO.address.city}, ${COMPANY_INFO.address.state}`,
      duration: '2-4 months',
      stipend: 'Certificate + Portfolio Building',
      skills: ['Photoshop', 'Illustrator', 'Canva', 'Figma', 'Creativity'],
      description: 'Build your portfolio while working on real design projects. Great opportunity to gain experience and industry exposure.',
      requirements: [
        'Currently pursuing degree in Design or related field',
        'Basic knowledge of design software',
        'Creative portfolio (can be academic projects)',
        'Passion for visual design',
        'Ability to take feedback and iterate'
      ]
    }
  ];

  const handleApply = (item) => {
    // Redirect to contact page or show application form
    window.location.href = '/contact';
  };

  const getIcon = (department) => {
    switch (department) {
      case 'Development': return 'fas fa-code';
      case 'Design': return 'fas fa-desktop';
      case 'Marketing': return 'fas fa-users';
      default: return 'fas fa-briefcase';
    }
  };

  return (
    <div className="careers-page">
      <SEO
        title="Careers - Maruti iT Zone | Join Our Amazing Team"
        description="Explore career opportunities at Maruti iT Zone. Join our team of passionate IT professionals and grow your career in web development, mobile apps, and cloud solutions."
        keywords="careers, jobs, Maruti iT Zone, IT jobs, web developer jobs, mobile app developer, cloud engineer"
        url="https://marutiitzone.com/careers"
      />

      {/* Hero Section */}
      <section className="bg-gradient-primary py-5 text-white text-center">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-8">
              <h1 className="display-3 fw-bold mb-4">Join Our <span className="text-warning">Team</span></h1>
              <p className="lead mb-4 opacity-90">
                Build your career with us and be part of innovative projects that make a difference.
                We offer exciting opportunities for both experienced professionals and fresh talent.
              </p>
              <div className="d-flex justify-content-center gap-3 flex-wrap">
                <button
                  className={`btn ${activeTab === 'jobs' ? 'btn-light' : 'btn-outline-light'} btn-lg rounded-pill px-4`}
                  onClick={() => setActiveTab('jobs')}
                >
                  <i className="fas fa-briefcase me-2"></i>
                  Job Openings
                </button>
                <button
                  className={`btn ${activeTab === 'internships' ? 'btn-light' : 'btn-outline-light'} btn-lg rounded-pill px-4`}
                  onClick={() => setActiveTab('internships')}
                >
                  <i className="fas fa-graduation-cap me-2"></i>
                  Internships
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Jobs/Internships Section */}
      <section className="py-5">
        <div className="container">
          {activeTab === 'jobs' ? (
            <>
              <div className="row mb-5">
                <div className="col-lg-8 mx-auto text-center">
                  <h2 className="display-5 fw-bold mb-4">Current Job Openings</h2>
                  <p className="lead text-muted">
                    Join our team of passionate professionals and work on cutting-edge projects
                  </p>
                </div>
              </div>
              <div className="row">
                {jobOpenings.map((job) => (
                  <div className="col-lg-6 mb-4" key={job.id}>
                    <div className="card h-100 shadow-sm border-0 rounded-4 bg-white">
                      <div className="card-header bg-light border-0 p-4">
                        <div className="d-flex justify-content-between align-items-start">
                          <div>
                            <h5 className="mb-1 fw-bold">{job.title}</h5>
                            <div className="d-flex align-items-center text-muted">
                              <i className={`${getIcon(job.department)} me-2`}></i>
                              <span>{job.department}</span>
                            </div>
                          </div>
                          <span className="badge bg-primary rounded-pill px-3">{job.type}</span>
                        </div>
                      </div>
                      <div className="card-body p-4">
                        <div className="mb-3">
                          <div className="d-flex align-items-center mb-2">
                            <i className="fas fa-map-marker-alt text-muted me-2"></i>
                            <span>{job.location}</span>
                          </div>
                          <div className="d-flex align-items-center mb-2">
                            <i className="fas fa-clock text-muted me-2"></i>
                            <span>{job.experience}</span>
                          </div>
                          <div className="d-flex align-items-center mb-3">
                            <i className="fas fa-rupee-sign text-muted me-2"></i>
                            <span>{job.salary}</span>
                          </div>
                        </div>

                        <p className="text-muted mb-3">{job.description}</p>

                        <div className="mb-3">
                          <h6 className="fw-bold">Required Skills:</h6>
                          <div className="d-flex flex-wrap gap-1">
                            {job.skills.map((skill, index) => (
                              <span key={index} className="badge bg-secondary rounded-pill me-1 mb-1">
                                {skill}
                              </span>
                            ))}
                          </div>
                        </div>

                        <button
                          className="btn btn-primary w-100 rounded-pill"
                          onClick={() => handleApply(job)}
                        >
                          <i className="fas fa-paper-plane me-2"></i>
                          Apply Now
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <>
              <div className="row mb-5">
                <div className="col-lg-8 mx-auto text-center">
                  <h2 className="display-5 fw-bold mb-4">Internship Opportunities</h2>
                  <p className="lead text-muted">
                    Start your career journey with hands-on experience and mentorship
                  </p>
                </div>
              </div>
              <div className="row">
                {internships.map((internship) => (
                  <div className="col-lg-6 mb-4" key={internship.id}>
                    <div className="card h-100 shadow-sm border-0 rounded-4 bg-white">
                      <div className="card-header bg-light border-0 p-4">
                        <div className="d-flex justify-content-between align-items-start">
                          <div>
                            <h5 className="mb-1 fw-bold">{internship.title}</h5>
                            <div className="d-flex align-items-center text-muted">
                              <i className={`${getIcon(internship.department)} me-2`}></i>
                              <span>{internship.department}</span>
                            </div>
                          </div>
                          <span className={`badge rounded-pill px-3 ${internship.stipend.includes('Certificate') ? 'bg-warning' : 'bg-success'}`}>
                            {internship.stipend.includes('Certificate') ? 'Unpaid' : 'Paid'}
                          </span>
                        </div>
                      </div>
                      <div className="card-body p-4">
                        <div className="mb-3">
                          <div className="d-flex align-items-center mb-2">
                            <i className="fas fa-map-marker-alt text-muted me-2"></i>
                            <span>{internship.location}</span>
                          </div>
                          <div className="d-flex align-items-center mb-2">
                            <i className="fas fa-clock text-muted me-2"></i>
                            <span>{internship.duration}</span>
                          </div>
                          <div className="d-flex align-items-center mb-3">
                            <i className="fas fa-rupee-sign text-muted me-2"></i>
                            <span>{internship.stipend}</span>
                          </div>
                        </div>

                        <p className="text-muted mb-3">{internship.description}</p>

                        <div className="mb-3">
                          <h6 className="fw-bold">Skills You'll Learn:</h6>
                          <div className="d-flex flex-wrap gap-1">
                            {internship.skills.map((skill, index) => (
                              <span key={index} className="badge bg-secondary rounded-pill me-1 mb-1">
                                {skill}
                              </span>
                            ))}
                          </div>
                        </div>

                        <button
                          className="btn btn-primary w-100 rounded-pill"
                          onClick={() => handleApply(internship)}
                        >
                          <i className="fas fa-paper-plane me-2"></i>
                          Apply Now
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </section>
    </div>
  );
};

export default Careers;
