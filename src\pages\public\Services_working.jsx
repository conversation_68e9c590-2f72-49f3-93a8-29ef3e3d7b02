import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Card, CardBody, Button, Badge } from 'reactstrap';
import { FaCode, FaMobile, FaCloud, FaShoppingCart, FaPalette, FaBullhorn, FaCheck, FaArrowRight, FaStar } from 'react-icons/fa';
import { Link } from 'react-router-dom';

const Services = () => {
  const services = [
    {
      id: 1,
      title: 'Web Development',
      description: 'Custom websites and web applications built with modern technologies and best practices.',
      price: '$2,000 - $15,000',
      duration: '4-12 weeks',
      popular: true,
      icon: FaCode,
      color: '#5e72e4',
      features: [
        'Responsive Design',
        'SEO Optimization',
        'Fast Loading Speed',
        'Cross-browser Compatibility',
        'Content Management System',
        'Security Implementation'
      ]
    },
    {
      id: 2,
      title: 'Mobile App Development',
      description: 'Native and cross-platform mobile applications for iOS and Android devices.',
      price: '$5,000 - $25,000',
      duration: '8-16 weeks',
      popular: false,
      icon: FaMobile,
      color: '#2dce89',
      features: [
        'Native Performance',
        'Push Notifications',
        'Offline Functionality',
        'App Store Optimization',
        'User Analytics',
        'Social Media Integration'
      ]
    },
    {
      id: 3,
      title: 'Cloud Services',
      description: 'Scalable cloud infrastructure and migration services for modern businesses.',
      price: '$1,000 - $10,000',
      duration: '2-8 weeks',
      popular: false,
      icon: FaCloud,
      color: '#11cdef',
      features: [
        'Cloud Migration',
        'Auto Scaling',
        'Backup Solutions',
        'Security Monitoring',
        'Cost Optimization',
        '24/7 Support'
      ]
    },
    {
      id: 4,
      title: 'E-commerce Solutions',
      description: 'Complete online store solutions with payment integration and inventory management.',
      price: '$3,000 - $20,000',
      duration: '6-14 weeks',
      popular: true,
      icon: FaShoppingCart,
      color: '#fb6340',
      features: [
        'Payment Gateway Integration',
        'Inventory Management',
        'Order Tracking',
        'Customer Management',
        'Analytics Dashboard',
        'Mobile Responsive'
      ]
    },
    {
      id: 5,
      title: 'UI/UX Design',
      description: 'User-centered design solutions that enhance user experience and engagement.',
      price: '$1,500 - $8,000',
      duration: '3-8 weeks',
      popular: false,
      icon: FaPalette,
      color: '#8965e0',
      features: [
        'User Research',
        'Wireframing',
        'Prototyping',
        'Visual Design',
        'Usability Testing',
        'Design System'
      ]
    },
    {
      id: 6,
      title: 'Digital Marketing',
      description: 'Comprehensive digital marketing strategies to grow your online presence.',
      price: '$500 - $5,000/month',
      duration: 'Ongoing',
      popular: false,
      icon: FaBullhorn,
      color: '#ffd600',
      features: [
        'SEO Optimization',
        'Social Media Marketing',
        'Content Marketing',
        'PPC Advertising',
        'Email Marketing',
        'Analytics & Reporting'
      ]
    }
  ];

  return (
    <div className="services-page">
      {/* Hero Section */}
      <section 
        className="hero-section py-5"
        style={{
          background: 'linear-gradient(135deg, #5e72e4 0%, #2dce89 100%)',
          minHeight: '70vh',
          display: 'flex',
          alignItems: 'center'
        }}
      >
        <Container className="text-center">
          <h1 className="display-3 fw-bold text-white mb-4">
            Professional IT Solutions
          </h1>
          <p className="lead text-white mx-auto" style={{ maxWidth: '600px' }}>
            We provide comprehensive technology services to help your business grow and succeed in the digital world.
            From web development to cloud solutions, we've got you covered.
          </p>
        </Container>
      </section>

      {/* Services Grid */}
      <section className="py-5 bg-light">
        <Container>
          <Row>
            {services.map((service) => {
              const IconComponent = service.icon;
              return (
                <Col key={service.id} md={6} lg={4} className="mb-4">
                  <Card className="h-100 border-0 shadow-sm position-relative">
                    {service.popular && (
                      <Badge 
                        color="primary" 
                        className="position-absolute top-0 end-0 m-3"
                        style={{ zIndex: 1 }}
                      >
                        <FaStar className="me-1" />
                        Popular
                      </Badge>
                    )}

                    <CardBody className="p-4">
                      <div 
                        className="rounded-circle d-inline-flex align-items-center justify-content-center mb-3"
                        style={{
                          width: '64px',
                          height: '64px',
                          backgroundColor: `${service.color}20`,
                          color: service.color
                        }}
                      >
                        <IconComponent size={24} />
                      </div>

                      <h3 className="h4 fw-bold mb-3 text-dark">
                        {service.title}
                      </h3>

                      <p className="text-muted mb-3">
                        {service.description}
                      </p>

                      <div className="mb-3">
                        <div className="d-flex justify-content-between align-items-center mb-2">
                          <span className="fw-semibold text-dark">Price Range:</span>
                          <span className="text-primary fw-bold">{service.price}</span>
                        </div>
                        <div className="d-flex justify-content-between align-items-center">
                          <span className="fw-semibold text-dark">Duration:</span>
                          <span className="text-muted">{service.duration}</span>
                        </div>
                      </div>

                      <div className="mb-3">
                        <h5 className="fw-semibold mb-2 text-dark">
                          Key Features:
                        </h5>
                        <ul className="list-unstyled">
                          {service.features.slice(0, 4).map((feature, idx) => (
                            <li key={idx} className="d-flex align-items-center small mb-1">
                              <FaCheck className="text-success me-2 flex-shrink-0" />
                              <span className="text-muted">{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <Button
                        tag={Link}
                        to="/contact-us"
                        color="primary"
                        className="w-100 rounded-pill"
                      >
                        Get Quote
                        <FaArrowRight className="ms-2" />
                      </Button>
                    </CardBody>
                  </Card>
                </Col>
              );
            })}
          </Row>
        </Container>
      </section>

      {/* CTA Section */}
      <section 
        className="py-5 text-white text-center"
        style={{
          background: 'linear-gradient(135deg, #5e72e4 0%, #2dce89 100%)'
        }}
      >
        <Container>
          <h2 className="display-4 fw-bold mb-4">
            Ready to Get Started?
          </h2>
          <p className="lead mb-4" style={{ maxWidth: '600px', margin: '0 auto' }}>
            Let's discuss your project requirements and create a custom solution that fits your needs and budget.
          </p>
          <div className="d-flex flex-wrap justify-content-center gap-3">
            <Button
              tag={Link}
              to="/contact-us"
              size="lg"
              className="btn-light rounded-pill"
            >
              Get Free Quote
              <FaArrowRight className="ms-2" />
            </Button>
            <Button
              tag={Link}
              to="/portfolio"
              size="lg"
              className="btn-outline-light rounded-pill"
            >
              View Our Work
            </Button>
          </div>
        </Container>
      </section>
    </div>
  );
};

export default Services;
