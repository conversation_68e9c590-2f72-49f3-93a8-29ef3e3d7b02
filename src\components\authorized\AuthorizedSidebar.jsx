import { Nav, NavItem, NavLink } from 'reactstrap';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import logoImage from '../../assets/img/logo.png';

const AuthorizedSidebar = ({ isOpen, toggle }) => {
  const location = useLocation();
  const { logout } = useAuth();

  const menuItems = [
    { path: '/authorized', label: 'Dashboard', icon: '📊' },
    { path: '/authorized/services', label: 'Manage Services', icon: '⚙️' },
    { path: '/authorized/portfolio', label: 'Manage Portfolio', icon: '💼' },
    { path: '/authorized/team', label: 'Manage Team', icon: '👥' },
    { path: '/authorized/settings', label: 'Settings', icon: '🔧' }
  ];

  const isActive = (path) => location.pathname === path;

  const handleLogout = () => {
    logout();
  };

  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && (
        <div 
          className="d-md-none position-fixed w-100 h-100"
          style={{ 
            background: 'rgba(0,0,0,0.5)', 
            zIndex: 999,
            top: 0,
            left: 0
          }}
          onClick={toggle}
        />
      )}
      
      {/* Sidebar */}
      <div
        className={`authorized-sidebar ${isOpen ? 'show' : ''}`}
        style={{
          background: 'var(--dark)',
          color: 'white',
          width: '250px',
          minHeight: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          zIndex: 1000,
          transform: isOpen ? 'translateX(0)' : 'translateX(-100%)',
          transition: 'transform 0.3s ease'
        }}
      >
        {/* Logo */}
        <div className="p-4 border-bottom" style={{ borderColor: 'rgba(255,255,255,0.2)' }}>
          <div className="d-flex align-items-center">
            <img
              src={logoImage}
              alt="Maruti IT Zone Logo"
              style={{
                width: '40px',
                height: '40px',
                marginRight: '10px',
                objectFit: 'contain'
              }}
              onError={(e) => {
                // Fallback to SVG logo if PNG fails
                e.target.src = '/logo.svg';
                e.target.onerror = () => {
                  // Final fallback to gradient circle
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'flex';
                };
              }}
            />
            <div
              style={{
                width: '40px',
                height: '40px',
                background: 'linear-gradient(135deg, #FF5722 0%, #2196F3 100%)',
                borderRadius: '50%',
                display: 'none',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1.2rem',
                marginRight: '10px'
              }}
            >
              M
            </div>
            <div>
              <h5 className="mb-0" style={{ color: 'white' }}>
                Maruti IT Zone
              </h5>
              <small style={{ color: 'rgba(255,255,255,0.7)' }}>
                Authorized Panel
              </small>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <Nav vertical className="p-3">
          {menuItems.map((item, index) => (
            <NavItem key={index} className="mb-2">
              <NavLink
                tag={Link}
                to={item.path}
                className={`d-flex align-items-center p-3 rounded text-decoration-none ${
                  isActive(item.path) 
                    ? 'bg-primary text-white' 
                    : 'text-light hover-bg-secondary'
                }`}
                style={{
                  transition: 'all 0.3s ease',
                  backgroundColor: isActive(item.path) ? 'var(--primary)' : 'transparent',
                  color: 'white'
                }}
                onClick={() => window.innerWidth < 768 && toggle()}
              >
                <span className="me-3" style={{ fontSize: '1.2rem' }}>
                  {item.icon}
                </span>
                {item.label}
              </NavLink>
            </NavItem>
          ))}
        </Nav>

        {/* Logout */}
        <div className="mt-auto p-3 border-top" style={{ borderColor: 'rgba(255,255,255,0.2)' }}>
          <button
            onClick={handleLogout}
            className="btn w-100 d-flex align-items-center justify-content-center"
            style={{
              background: 'transparent',
              border: '1px solid rgba(255,255,255,0.3)',
              color: 'white'
            }}
          >
            <span className="me-2">🚪</span>
            Logout
          </button>
        </div>
      </div>

      {/* Desktop Sidebar Styles */}
      <style jsx>{`
        @media (min-width: 768px) {
          .authorized-sidebar {
            transform: translateX(0) !important;
          }
        }
        
        .hover-bg-secondary:hover {
          background-color: rgba(255, 255, 255, 0.1) !important;
        }
      `}</style>
    </>
  );
};

export default AuthorizedSidebar;
