<?xml version="1.0" encoding="UTF-8"?>
<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Main gradient for the logo -->
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF5722;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FF7043;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2196F3;stop-opacity:1" />
    </linearGradient>

    <!-- Shadow filter -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="2" stdDeviation="3" flood-color="rgba(0,0,0,0.25)"/>
    </filter>

    <!-- Inner glow effect -->
    <filter id="innerGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Background circle with gradient -->
  <circle cx="20" cy="20" r="18" fill="url(#logoGradient)" stroke="rgba(255,255,255,0.2)" stroke-width="1" filter="url(#shadow)"/>

  <!-- Inner circle for depth -->
  <circle cx="20" cy="20" r="15" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/>

  <!-- Company initial with enhanced styling -->
  <text x="20" y="27" font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif" font-size="18" font-weight="700" fill="white" text-anchor="middle" filter="url(#innerGlow)">M</text>

  <!-- Tech-inspired decorative elements -->
  <g opacity="0.4">
    <!-- Top right accent -->
    <circle cx="30" cy="12" r="1.2" fill="rgba(255,255,255,0.6)"/>
    <circle cx="30" cy="12" r="0.6" fill="rgba(255,255,255,0.9)"/>

    <!-- Bottom left accent -->
    <circle cx="10" cy="28" r="0.8" fill="rgba(255,255,255,0.5)"/>

    <!-- Bottom right accent -->
    <circle cx="32" cy="30" r="0.6" fill="rgba(255,255,255,0.4)"/>

    <!-- Additional small accents for tech feel -->
    <circle cx="8" cy="15" r="0.4" fill="rgba(255,255,255,0.3)"/>
    <circle cx="28" cy="32" r="0.5" fill="rgba(255,255,255,0.35)"/>
  </g>

  <!-- Subtle border highlight -->
  <circle cx="20" cy="20" r="17.5" fill="none" stroke="rgba(255,255,255,0.15)" stroke-width="0.5"/>
</svg>
