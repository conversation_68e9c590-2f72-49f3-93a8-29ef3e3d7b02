import { Link } from 'react-router-dom';
import SEO from '../../components/common/SEO';

const Sitemap = () => {
  const getIconClass = (title) => {
    switch (title) {
      case 'Main Pages': return 'fas fa-home';
      case 'User Account': return 'fas fa-users';
      case 'Company Info': return 'fas fa-info-circle';
      case 'Admin Panel': return 'fas fa-cogs';
      default: return 'fas fa-file';
    }
  };

  const siteStructure = [
    {
      title: 'Main Pages',
      icon: FaHome,
      pages: [
        { name: 'Home', path: '/', description: 'Welcome page with company overview' },
        { name: 'About Us', path: '/about-us', description: 'Learn about our company and team' },
        { name: 'Services', path: '/services', description: 'Our IT solutions and services' },
        { name: 'Portfolio', path: '/portfolio', description: 'Showcase of our work' },
        { name: 'Our Team', path: '/team', description: 'Meet our talented team' },
        { name: 'Careers', path: '/careers', description: 'Join our amazing team' },
        { name: 'Contact Us', path: '/contact-us', description: 'Get in touch with us' }
      ]
    },
    {
      title: 'User Account',
      icon: FaUsers,
      pages: [
        { name: 'Login', path: '/login', description: 'User authentication' },
        { name: 'Register', path: '/register', description: 'Create new account' },
        { name: 'Dashboard', path: '/dashboard', description: 'User dashboard (protected)' }
      ]
    },
    {
      title: 'Company Info',
      icon: FaInfoCircle,
      pages: [
        { name: 'Careers', path: '/careers', description: 'Join our team' },
        { name: 'Privacy Policy', path: '/privacy-policy', description: 'How we protect your data' },
        { name: 'Terms of Service', path: '/terms-of-service', description: 'Terms and conditions' },
        { name: 'Sitemap', path: '/sitemap', description: 'Website navigation guide' }
      ]
    },
    {
      title: 'Admin Panel',
      icon: FaCog,
      pages: [
        { name: 'Admin Dashboard', path: '/authorized', description: 'Administrative dashboard (protected)' },
        { name: 'Manage Services', path: '/authorized/services', description: 'Service management' },
        { name: 'Manage Portfolio', path: '/authorized/portfolio', description: 'Portfolio management' },
        { name: 'Manage Team', path: '/authorized/team', description: 'Team management' },
        { name: 'Settings', path: '/authorized/settings', description: 'System settings' }
      ]
    }
  ];

  return (
    <div className="sitemap-page">
      <SEO
        title="Sitemap - Maruti IT Zone | Website Navigation"
        description="Navigate through all pages and sections of Maruti IT Zone website. Find all our services, company information, and resources in one place."
        keywords="sitemap, navigation, website map, pages, Maruti IT Zone, site structure"
        url="https://marutiitzone.com/sitemap"
      />

      {/* Hero Section */}
      <section className="bg-gradient-primary py-5 text-white">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-8 mx-auto text-center">
              <div className="hero-icon mb-4">
                <i className="fas fa-sitemap" style={{ fontSize: '4rem', color: 'rgba(255,255,255,0.9)' }}></i>
              </div>
              <h1 className="display-3 fw-bold text-white mb-4">
                Website Sitemap
              </h1>
              <p className="lead text-white mb-4 opacity-90">
                Navigate through all pages and sections of Maruti iT Zone website. Find all our services, company information, and resources in one place.
              </p>
              <div className="d-flex flex-wrap justify-content-center gap-3">
                <Link to="/" className="btn btn-light btn-lg rounded-pill px-4">
                  <i className="fas fa-home me-2"></i>
                  Back to Home
                </Link>
                <Link to="/contact-us" className="btn btn-outline-light btn-lg rounded-pill px-4">
                  <i className="fas fa-envelope me-2"></i>
                  Contact Us
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>



      {/* Main Content */}
      <section className="py-5 bg-light">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-10">
              <div className="text-center mb-5">
                <div className="d-inline-flex align-items-center justify-content-center bg-primary bg-opacity-10 rounded-circle mb-3" style={{ width: '80px', height: '80px' }}>
                  <i className="fas fa-sitemap text-primary" style={{ fontSize: '2rem' }}></i>
                </div>
                <h2 className="h1 fw-bold mb-3 text-dark">
                  Website Sitemap
                </h2>
                <p className="lead text-muted">
                  Navigate through all pages and sections of our website
                </p>
              </div>

              <div className="row g-4">
                {siteStructure.map((section, index) => (
                  <div className="col-lg-6" key={index}>
                    <div className="h-100 shadow-lg border-0 bg-white rounded-4 p-4">
                      <div className="d-flex align-items-center mb-4">
                        <div className="bg-primary bg-opacity-10 rounded-circle p-3 me-3">
                          <i className={`${getIconClass(section.title)} text-primary`} style={{ fontSize: '1.5rem' }}></i>
                        </div>
                        <h3 className="h4 fw-bold mb-0 text-dark">{section.title}</h3>
                      </div>
                        
                        <div className="sitemap-links">
                          {section.pages.map((page, pageIndex) => (
                            <div key={pageIndex} className="d-flex align-items-start mb-3 p-3 rounded bg-light">
                              <div className="me-3 mt-1">
                                <div className="bg-primary rounded-circle" style={{ width: '8px', height: '8px' }}></div>
                              </div>
                              <div className="flex-grow-1">
                                <Link
                                  to={page.path}
                                  className="text-decoration-none"
                                >
                                  <h5 className="fw-semibold mb-1 text-dark hover-primary">
                                    {page.name}
                                  </h5>
                                </Link>
                                <p className="text-muted small mb-0">
                                  {page.description}
                                </p>
                                <small className="text-primary">
                                  {page.path}
                                </small>
                              </div>
                            </div>
                          ))}
                        </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Quick Stats */}
              <div className="row mt-5">
                <div className="col">
                  <div className="shadow-lg border-0 bg-white rounded-4 p-5 text-center">
                    <h3 className="fw-bold mb-4 text-dark">Website Statistics</h3>
                    <div className="row">
                      <div className="col-md-3 mb-3">
                        <div>
                          <div className="h2 fw-bold text-primary mb-2">20+</div>
                          <small className="text-muted">
                            Total Pages
                          </small>
                        </div>
                      </div>
                      <div className="col-md-3 mb-3">
                        <div>
                          <div className="h2 fw-bold text-success mb-2">4</div>
                          <small className="text-muted">
                            Main Sections
                          </small>
                        </div>
                      </div>
                      <div className="col-md-3 mb-3">
                        <div>
                          <div className="h2 fw-bold text-warning mb-2">100%</div>
                          <small className="text-muted">
                            Mobile Friendly
                          </small>
                        </div>
                      </div>
                      <div className="col-md-3 mb-3">
                        <div>
                          <div className="h2 fw-bold text-info mb-2">24/7</div>
                          <small className="text-muted">
                            Support
                          </small>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-primary py-5 text-center text-white">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-8">
              <h2 className="display-4 fw-bold mb-4">
                Need Help Finding Something?
              </h2>
              <p className="lead mb-4 opacity-90">
                Can't find what you're looking for? Our team is here to help you navigate our services and solutions.
              </p>
              <div className="d-flex flex-wrap justify-content-center gap-3">
                <Link
                  to="/contact-us"
                  className="btn btn-light btn-lg rounded-pill px-4"
                >
                  <i className="fas fa-headset me-2"></i>
                  Contact Support
                </Link>
                <Link
                  to="/services"
                  className="btn btn-outline-light btn-lg rounded-pill px-4"
                >
                  <i className="fas fa-cogs me-2"></i>
                  View Services
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Sitemap;
