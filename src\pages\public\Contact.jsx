import { useState } from 'react';
import SEO from '../../components/common/SEO';
import { COMPANY_INFO } from '../../assets/js/constants';

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [showAlert, setShowAlert] = useState(false);
  const [alertType, setAlertType] = useState('success');
  const [alertMessage, setAlertMessage] = useState('');

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.name || !formData.email || !formData.message) {
      setAlertType('danger');
      setAlertMessage('Please fill in all required fields.');
      setShowAlert(true);
      return;
    }

    // Simulate form submission
    console.log('Form submitted:', formData);
    
    setAlertType('success');
    setAlertMessage('Thank you for your message! We will get back to you soon.');
    setShowAlert(true);
    
    // Reset form
    setFormData({
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: ''
    });

    // Hide alert after 5 seconds
    setTimeout(() => {
      setShowAlert(false);
    }, 5000);
  };

  const contactInfo = [
    {
      icon: 'envelope',
      title: 'Email Us',
      details: COMPANY_INFO.email,
      description: 'Send us an email anytime!'
    },
    {
      icon: 'phone',
      title: 'Call Us',
      details: COMPANY_INFO.phone,
      description: 'Mon-Fri from 9am to 6pm'
    },
    {
      icon: 'map-marker-alt',
      title: 'Visit Us',
      details: COMPANY_INFO.address.fullAddress,
      description: 'Come say hello at our office'
    },
    {
      icon: 'clock',
      title: 'Working Hours',
      details: 'Mon - Fri: 9:00 AM - 6:00 PM',
      description: 'Saturday: 10:00 AM - 4:00 PM'
    }
  ];

  return (
    <div className="contact-page">
      <SEO
        title="Contact Us - Maruti iT Zone | Get In Touch"
        description="Contact Maruti iT Zone for all your IT needs. We're here to help with web development, mobile apps, cloud solutions, and digital marketing services."
        keywords="contact, Maruti iT Zone, IT services, web development, mobile apps, cloud solutions"
        url="https://marutiitzone.com/contact"
      />

      {/* Hero Section */}
      <section className="bg-gradient-primary py-5 text-white text-center">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-8">
              <h1 className="display-3 fw-bold mb-4">Contact Us</h1>
              <p className="lead mb-4 opacity-90">
                Ready to start your next project? Get in touch with us today and let's discuss
                how we can help bring your vision to life.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Info Section */}
      <section className="py-5 bg-light">
        <div className="container">
          <div className="row g-4">
            {contactInfo.map((info, index) => (
              <div key={index} className="col-md-6 col-lg-3">
                <div className="card-modern h-100 text-center p-4">
                  <div className="icon-circle icon-circle-primary mx-auto mb-3">
                    <i className={`fas fa-${info.icon} fs-4`}></i>
                  </div>
                  <h5 className="fw-bold text-dark mb-2">{info.title}</h5>
                  <p className="text-gradient fw-semibold mb-1">{info.details}</p>
                  <p className="text-muted small mb-0">{info.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-5 bg-white">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-8">
              <div className="text-center mb-5">
                <h2 className="display-4 fw-bold text-dark mb-4">Send Us a Message</h2>
                <p className="lead text-muted">
                  Have a question or want to work together? Fill out the form below and we'll get back to you as soon as possible.
                </p>
              </div>

              <div className="card border-0 shadow-lg rounded-4">
                <div className="card-body p-5">
                  {showAlert && (
                    <div className={`alert alert-${alertType === 'danger' ? 'danger' : 'success'} mb-4`}>
                      {alertMessage}
                    </div>
                  )}

                  <form onSubmit={handleSubmit}>
                    <div className="row">
                      <div className="col-md-6">
                        <div className="mb-3">
                          <label htmlFor="name" className="form-label fw-semibold">
                            <i className="fas fa-user me-2"></i>
                            Full Name *
                          </label>
                          <input
                            type="text"
                            name="name"
                            id="name"
                            placeholder="Enter your full name"
                            value={formData.name}
                            onChange={handleChange}
                            required
                            className="form-control form-control-lg"
                          />
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div className="mb-3">
                          <label htmlFor="email" className="form-label fw-semibold">
                            <i className="fas fa-envelope me-2"></i>
                            Email Address *
                          </label>
                          <input
                            type="email"
                            name="email"
                            id="email"
                            placeholder="Enter your email address"
                            value={formData.email}
                            onChange={handleChange}
                            required
                            className="form-control form-control-lg"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="row">
                      <div className="col-md-6">
                        <div className="mb-3">
                          <label htmlFor="phone" className="form-label fw-semibold">
                            <i className="fas fa-phone me-2"></i>
                            Phone Number
                          </label>
                          <input
                            type="tel"
                            name="phone"
                            id="phone"
                            placeholder="Enter your phone number"
                            value={formData.phone}
                            onChange={handleChange}
                            className="form-control form-control-lg"
                          />
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div className="mb-3">
                          <label htmlFor="subject" className="form-label fw-semibold">
                            <i className="fas fa-tag me-2"></i>
                            Subject
                          </label>
                          <input
                            type="text"
                            name="subject"
                            id="subject"
                            placeholder="Enter subject"
                            value={formData.subject}
                            onChange={handleChange}
                            className="form-control form-control-lg"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="mb-3">
                      <label htmlFor="message" className="form-label fw-semibold">
                        <i className="fas fa-comment me-2"></i>
                        Message *
                      </label>
                      <textarea
                        name="message"
                        id="message"
                        rows="6"
                        placeholder="Tell us about your project or ask us a question..."
                        value={formData.message}
                        onChange={handleChange}
                        required
                        className="form-control form-control-lg"
                      ></textarea>
                    </div>

                    <div className="text-center">
                      <button
                        type="submit"
                        className="btn btn-primary btn-lg rounded-pill px-5"
                      >
                        <i className="fas fa-paper-plane me-2"></i>
                        Send Message
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-primary py-5 text-center text-white">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-8">
              <h2 className="display-4 fw-bold mb-4">Ready to Start Your Project?</h2>
              <p className="lead mb-4">
                Let's turn your ideas into reality. Contact us today for a free consultation 
                and see how we can help your business grow.
              </p>
              <div className="d-flex flex-wrap justify-content-center gap-3">
                <a
                  href={`tel:${COMPANY_INFO.phone.replace(/[^+\d]/g, '')}`}
                  className="btn btn-light btn-lg rounded-pill px-4"
                >
                  <i className="fas fa-phone me-2"></i>
                  Call Now
                </a>
                <a
                  href={`mailto:${COMPANY_INFO.email}`}
                  className="btn btn-outline-light btn-lg rounded-pill px-4"
                >
                  <i className="fas fa-envelope me-2"></i>
                  Email Us
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Contact;
