import { useState } from 'react';
import { Contain<PERSON>, <PERSON>, <PERSON>, <PERSON>, CardBody, CardHeader, <PERSON><PERSON>, Table } from 'reactstrap';

const ManageTeam = () => {
  const [teamMembers] = useState([
    {
      id: 1,
      name: '<PERSON><PERSON>',
      position: 'CEO & Founder',
      email: 'r<PERSON><PERSON>@marutiitzone.com',
      phone: '+91 98765 43210',
      department: 'Management',
      joinDate: '2019-01-01',
      status: 'Active',
      avatar: '👨‍💼'
    },
    {
      id: 2,
      name: '<PERSON><PERSON>',
      position: 'Chief Technology Officer',
      email: '<EMAIL>',
      phone: '+91 98765 43211',
      department: 'Technology',
      joinDate: '2019-02-15',
      status: 'Active',
      avatar: '👩‍💻'
    },
    {
      id: 3,
      name: '<PERSON><PERSON> <PERSON>',
      position: 'Head of Operations',
      email: '<EMAIL>',
      phone: '+91 98765 43212',
      department: 'Operations',
      joinDate: '2019-03-01',
      status: 'Active',
      avatar: '👨‍💼'
    },
    {
      id: 4,
      name: '<PERSON><PERSON><PERSON>',
      position: 'Senior Full Stack Developer',
      email: 's<PERSON><EMAIL>',
      phone: '+91 98765 43213',
      department: 'Development',
      joinDate: '2020-01-15',
      status: 'Active',
      avatar: '👩‍💻'
    },
    {
      id: 5,
      name: 'Rohit <PERSON>',
      position: 'Mobile App Developer',
      email: '<EMAIL>',
      phone: '+91 98765 43214',
      department: 'Development',
      joinDate: '2020-06-01',
      status: 'Active',
      avatar: '👨‍💻'
    },
    {
      id: 6,
      name: 'Kavya Reddy',
      position: 'UI/UX Designer',
      email: '<EMAIL>',
      phone: '+91 98765 43215',
      department: 'Design',
      joinDate: '2021-01-10',
      status: 'Active',
      avatar: '👩‍🎨'
    }
  ]);

  const departments = ['All', 'Management', 'Technology', 'Operations', 'Development', 'Design'];
  const [selectedDepartment, setSelectedDepartment] = useState('All');

  const filteredMembers = selectedDepartment === 'All' 
    ? teamMembers 
    : teamMembers.filter(member => member.department === selectedDepartment);

  const getDepartmentColor = (department) => {
    switch (department) {
      case 'Management': return 'primary';
      case 'Technology': return 'success';
      case 'Operations': return 'warning';
      case 'Development': return 'info';
      case 'Design': return 'secondary';
      default: return 'light';
    }
  };

  return (
    <div className="manage-team-page">
      <Container fluid>
        <Row className="mb-4">
          <Col>
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <h2 className="mb-1">Manage Team</h2>
                <p className="text-muted mb-0">Add, edit, or remove team members</p>
              </div>
              <Button color="primary" className="btn-primary-custom">
                ➕ Add New Member
              </Button>
            </div>
          </Col>
        </Row>

        {/* Department Filter */}
        <Row className="mb-4">
          <Col>
            <Card className="card-custom">
              <CardBody>
                <div className="d-flex flex-wrap gap-2">
                  <span className="me-3 fw-bold">Filter by Department:</span>
                  {departments.map((dept) => (
                    <Button
                      key={dept}
                      size="sm"
                      color={selectedDepartment === dept ? 'primary' : 'outline-secondary'}
                      onClick={() => setSelectedDepartment(dept)}
                    >
                      {dept}
                    </Button>
                  ))}
                </div>
              </CardBody>
            </Card>
          </Col>
        </Row>

        <Row>
          <Col>
            <Card className="card-custom">
              <CardHeader className="bg-white">
                <h5 className="mb-0">Team Members ({filteredMembers.length})</h5>
              </CardHeader>
              <CardBody className="p-0">
                <Table responsive hover className="mb-0">
                  <thead className="table-light">
                    <tr>
                      <th>Avatar</th>
                      <th>Name</th>
                      <th>Position</th>
                      <th>Department</th>
                      <th>Email</th>
                      <th>Phone</th>
                      <th>Join Date</th>
                      <th>Status</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredMembers.map((member) => (
                      <tr key={member.id}>
                        <td>
                          <div 
                            style={{
                              width: '50px',
                              height: '50px',
                              background: 'var(--gradient-primary)',
                              borderRadius: '50%',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              fontSize: '1.5rem',
                              color: 'white'
                            }}
                          >
                            {member.avatar}
                          </div>
                        </td>
                        <td>
                          <strong>{member.name}</strong>
                        </td>
                        <td>{member.position}</td>
                        <td>
                          <span className={`badge bg-${getDepartmentColor(member.department)}`}>
                            {member.department}
                          </span>
                        </td>
                        <td>
                          <a href={`mailto:${member.email}`} className="text-decoration-none">
                            {member.email}
                          </a>
                        </td>
                        <td>
                          <a href={`tel:${member.phone}`} className="text-decoration-none">
                            {member.phone}
                          </a>
                        </td>
                        <td>
                          <small className="text-muted">{member.joinDate}</small>
                        </td>
                        <td>
                          <span className={`badge bg-${member.status === 'Active' ? 'success' : 'secondary'}`}>
                            {member.status}
                          </span>
                        </td>
                        <td>
                          <Button 
                            size="sm" 
                            color="warning" 
                            className="me-2"
                          >
                            ✏️ Edit
                          </Button>
                          <Button 
                            size="sm" 
                            color="danger"
                          >
                            🗑️ Delete
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </CardBody>
            </Card>
          </Col>
        </Row>

        {/* Team Stats */}
        <Row className="mt-4">
          <Col lg={3} md={6} className="mb-3">
            <Card className="card-custom">
              <CardBody className="text-center">
                <h3 className="text-primary">15</h3>
                <p className="mb-0">Total Members</p>
              </CardBody>
            </Card>
          </Col>
          <Col lg={3} md={6} className="mb-3">
            <Card className="card-custom">
              <CardBody className="text-center">
                <h3 className="text-success">6</h3>
                <p className="mb-0">Developers</p>
              </CardBody>
            </Card>
          </Col>
          <Col lg={3} md={6} className="mb-3">
            <Card className="card-custom">
              <CardBody className="text-center">
                <h3 className="text-warning">3</h3>
                <p className="mb-0">Managers</p>
              </CardBody>
            </Card>
          </Col>
          <Col lg={3} md={6} className="mb-3">
            <Card className="card-custom">
              <CardBody className="text-center">
                <h3 className="text-info">2</h3>
                <p className="mb-0">Designers</p>
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default ManageTeam;
