import { useState } from 'react';
import { COMPANY_INFO } from '../../assets/js/constants';
import { Container, Row, Col, Card, CardBody, CardHeader, Form, FormGroup, Label, Input, Button, Nav, NavItem, NavLink, TabContent, TabPane } from 'reactstrap';

const Settings = () => {
  const [activeTab, setActiveTab] = useState('general');
  const [settings, setSettings] = useState({
    // General Settings
    companyName: COMPANY_INFO.name,
    companyEmail: COMPANY_INFO.email,
    companyPhone: COMPANY_INFO.phone,
    companyAddress: COMPANY_INFO.address.fullAddress,
    companyWebsite: COMPANY_INFO.website,
    
    // Email Settings
    smtpHost: 'smtp.gmail.com',
    smtpPort: '587',
    smtpUsername: '<EMAIL>',
    smtpPassword: '',
    
    // Notification Settings
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    
    // Security Settings
    twoFactorAuth: false,
    sessionTimeout: '30',
    passwordExpiry: '90'
  });

  const toggleTab = (tab) => {
    if (activeTab !== tab) setActiveTab(tab);
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Here you would typically save settings to backend
    alert('Settings saved successfully!');
  };

  return (
    <div className="settings-page">
      <Container fluid>
        <Row className="mb-4">
          <Col>
            <h2 className="mb-1">Settings</h2>
            <p className="text-muted mb-0">Manage your application settings and preferences</p>
          </Col>
        </Row>

        <Row>
          <Col lg={3} className="mb-4">
            <Card className="card-custom">
              <CardHeader className="bg-white">
                <h6 className="mb-0">Settings Menu</h6>
              </CardHeader>
              <CardBody className="p-0">
                <Nav vertical pills className="p-3">
                  <NavItem>
                    <NavLink
                      className={activeTab === 'general' ? 'active' : ''}
                      onClick={() => toggleTab('general')}
                      style={{ cursor: 'pointer' }}
                    >
                      🏢 General Settings
                    </NavLink>
                  </NavItem>
                  <NavItem>
                    <NavLink
                      className={activeTab === 'email' ? 'active' : ''}
                      onClick={() => toggleTab('email')}
                      style={{ cursor: 'pointer' }}
                    >
                      📧 Email Settings
                    </NavLink>
                  </NavItem>
                  <NavItem>
                    <NavLink
                      className={activeTab === 'notifications' ? 'active' : ''}
                      onClick={() => toggleTab('notifications')}
                      style={{ cursor: 'pointer' }}
                    >
                      🔔 Notifications
                    </NavLink>
                  </NavItem>
                  <NavItem>
                    <NavLink
                      className={activeTab === 'security' ? 'active' : ''}
                      onClick={() => toggleTab('security')}
                      style={{ cursor: 'pointer' }}
                    >
                      🔒 Security
                    </NavLink>
                  </NavItem>
                  <NavItem>
                    <NavLink
                      className={activeTab === 'backup' ? 'active' : ''}
                      onClick={() => toggleTab('backup')}
                      style={{ cursor: 'pointer' }}
                    >
                      💾 Backup & Restore
                    </NavLink>
                  </NavItem>
                </Nav>
              </CardBody>
            </Card>
          </Col>

          <Col lg={9}>
            <Card className="card-custom">
              <CardBody>
                <TabContent activeTab={activeTab}>
                  {/* General Settings */}
                  <TabPane tabId="general">
                    <h5 className="mb-4">General Settings</h5>
                    <Form onSubmit={handleSubmit}>
                      <Row>
                        <Col md={6}>
                          <FormGroup>
                            <Label for="companyName">Company Name</Label>
                            <Input
                              type="text"
                              name="companyName"
                              id="companyName"
                              value={settings.companyName}
                              onChange={handleChange}
                            />
                          </FormGroup>
                        </Col>
                        <Col md={6}>
                          <FormGroup>
                            <Label for="companyEmail">Company Email</Label>
                            <Input
                              type="email"
                              name="companyEmail"
                              id="companyEmail"
                              value={settings.companyEmail}
                              onChange={handleChange}
                            />
                          </FormGroup>
                        </Col>
                      </Row>
                      <Row>
                        <Col md={6}>
                          <FormGroup>
                            <Label for="companyPhone">Company Phone</Label>
                            <Input
                              type="tel"
                              name="companyPhone"
                              id="companyPhone"
                              value={settings.companyPhone}
                              onChange={handleChange}
                            />
                          </FormGroup>
                        </Col>
                        <Col md={6}>
                          <FormGroup>
                            <Label for="companyWebsite">Company Website</Label>
                            <Input
                              type="url"
                              name="companyWebsite"
                              id="companyWebsite"
                              value={settings.companyWebsite}
                              onChange={handleChange}
                            />
                          </FormGroup>
                        </Col>
                      </Row>
                      <FormGroup>
                        <Label for="companyAddress">Company Address</Label>
                        <Input
                          type="textarea"
                          name="companyAddress"
                          id="companyAddress"
                          rows="3"
                          value={settings.companyAddress}
                          onChange={handleChange}
                        />
                      </FormGroup>
                      <Button type="submit" className="btn-primary-custom">
                        Save General Settings
                      </Button>
                    </Form>
                  </TabPane>

                  {/* Email Settings */}
                  <TabPane tabId="email">
                    <h5 className="mb-4">Email Settings</h5>
                    <Form onSubmit={handleSubmit}>
                      <Row>
                        <Col md={6}>
                          <FormGroup>
                            <Label for="smtpHost">SMTP Host</Label>
                            <Input
                              type="text"
                              name="smtpHost"
                              id="smtpHost"
                              value={settings.smtpHost}
                              onChange={handleChange}
                            />
                          </FormGroup>
                        </Col>
                        <Col md={6}>
                          <FormGroup>
                            <Label for="smtpPort">SMTP Port</Label>
                            <Input
                              type="number"
                              name="smtpPort"
                              id="smtpPort"
                              value={settings.smtpPort}
                              onChange={handleChange}
                            />
                          </FormGroup>
                        </Col>
                      </Row>
                      <Row>
                        <Col md={6}>
                          <FormGroup>
                            <Label for="smtpUsername">SMTP Username</Label>
                            <Input
                              type="email"
                              name="smtpUsername"
                              id="smtpUsername"
                              value={settings.smtpUsername}
                              onChange={handleChange}
                            />
                          </FormGroup>
                        </Col>
                        <Col md={6}>
                          <FormGroup>
                            <Label for="smtpPassword">SMTP Password</Label>
                            <Input
                              type="password"
                              name="smtpPassword"
                              id="smtpPassword"
                              value={settings.smtpPassword}
                              onChange={handleChange}
                              placeholder="Enter SMTP password"
                            />
                          </FormGroup>
                        </Col>
                      </Row>
                      <Button type="submit" className="btn-primary-custom">
                        Save Email Settings
                      </Button>
                    </Form>
                  </TabPane>

                  {/* Notifications */}
                  <TabPane tabId="notifications">
                    <h5 className="mb-4">Notification Settings</h5>
                    <Form onSubmit={handleSubmit}>
                      <FormGroup check className="mb-3">
                        <Label check>
                          <Input
                            type="checkbox"
                            name="emailNotifications"
                            checked={settings.emailNotifications}
                            onChange={handleChange}
                          />
                          Enable Email Notifications
                        </Label>
                      </FormGroup>
                      <FormGroup check className="mb-3">
                        <Label check>
                          <Input
                            type="checkbox"
                            name="smsNotifications"
                            checked={settings.smsNotifications}
                            onChange={handleChange}
                          />
                          Enable SMS Notifications
                        </Label>
                      </FormGroup>
                      <FormGroup check className="mb-4">
                        <Label check>
                          <Input
                            type="checkbox"
                            name="pushNotifications"
                            checked={settings.pushNotifications}
                            onChange={handleChange}
                          />
                          Enable Push Notifications
                        </Label>
                      </FormGroup>
                      <Button type="submit" className="btn-primary-custom">
                        Save Notification Settings
                      </Button>
                    </Form>
                  </TabPane>

                  {/* Security */}
                  <TabPane tabId="security">
                    <h5 className="mb-4">Security Settings</h5>
                    <Form onSubmit={handleSubmit}>
                      <FormGroup check className="mb-3">
                        <Label check>
                          <Input
                            type="checkbox"
                            name="twoFactorAuth"
                            checked={settings.twoFactorAuth}
                            onChange={handleChange}
                          />
                          Enable Two-Factor Authentication
                        </Label>
                      </FormGroup>
                      <Row>
                        <Col md={6}>
                          <FormGroup>
                            <Label for="sessionTimeout">Session Timeout (minutes)</Label>
                            <Input
                              type="number"
                              name="sessionTimeout"
                              id="sessionTimeout"
                              value={settings.sessionTimeout}
                              onChange={handleChange}
                            />
                          </FormGroup>
                        </Col>
                        <Col md={6}>
                          <FormGroup>
                            <Label for="passwordExpiry">Password Expiry (days)</Label>
                            <Input
                              type="number"
                              name="passwordExpiry"
                              id="passwordExpiry"
                              value={settings.passwordExpiry}
                              onChange={handleChange}
                            />
                          </FormGroup>
                        </Col>
                      </Row>
                      <Button type="submit" className="btn-primary-custom">
                        Save Security Settings
                      </Button>
                    </Form>
                  </TabPane>

                  {/* Backup & Restore */}
                  <TabPane tabId="backup">
                    <h5 className="mb-4">Backup & Restore</h5>
                    <div className="mb-4">
                      <h6>Database Backup</h6>
                      <p className="text-muted">Create a backup of your database</p>
                      <Button className="btn-primary-custom me-2">
                        📥 Create Backup
                      </Button>
                      <Button color="secondary">
                        📤 Download Latest Backup
                      </Button>
                    </div>
                    <div className="mb-4">
                      <h6>Restore Database</h6>
                      <p className="text-muted">Restore database from a backup file</p>
                      <Input type="file" className="mb-2" accept=".sql,.db" />
                      <Button color="warning">
                        🔄 Restore Database
                      </Button>
                    </div>
                    <div>
                      <h6>System Information</h6>
                      <Table size="sm">
                        <tbody>
                          <tr>
                            <td>Last Backup:</td>
                            <td>2024-01-20 10:30 AM</td>
                          </tr>
                          <tr>
                            <td>Database Size:</td>
                            <td>45.2 MB</td>
                          </tr>
                          <tr>
                            <td>Storage Used:</td>
                            <td>2.1 GB / 10 GB</td>
                          </tr>
                        </tbody>
                      </Table>
                    </div>
                  </TabPane>
                </TabContent>
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default Settings;
