import { Link } from 'react-router-dom';
import SEO from '../../components/common/SEO';

const About = () => {
  const stats = [
    { number: '100+', label: 'Projects Completed' },
    { number: '50+', label: 'Happy Clients' },
    { number: '10+', label: 'Team Members' },
    { number: '5+', label: 'Years Experience' },
    { number: '24/7', label: 'Support Available' }
  ];

  const values = [
    {
      icon: 'lightbulb',
      title: 'Innovation',
      description: 'We embrace cutting-edge technologies and creative solutions to solve complex challenges.'
    },
    {
      icon: 'handshake',
      title: 'Integrity',
      description: 'We build trust through transparency, honesty, and ethical business practices.'
    },
    {
      icon: 'bullseye',
      title: 'Excellence',
      description: 'We strive for perfection in every project, delivering quality that exceeds expectations.'
    },
    {
      icon: 'heart',
      title: 'Passion',
      description: 'We love what we do and bring enthusiasm to every project we undertake.'
    }
  ];

  return (
    <div className="about-page">
      <SEO
        title="About Us - Maruti iT Zone | Leading IT Solutions Provider"
        description="Learn about Maruti iT Zone, a leading IT solutions provider specializing in web development, mobile apps, cloud solutions, and digital marketing services."
        keywords="about Maruti iT Zone, IT company, web development company, mobile app development, cloud solutions"
        url="https://marutiitzone.com/about"
      />

      {/* Hero Section */}
      <section className="bg-gradient-primary py-5 text-white">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-6">
              <h1 className="display-3 fw-bold text-white mb-4">
                About <span className="text-warning">Maruti iT Zone</span>
              </h1>
              <p className="lead text-white mb-4 opacity-90">
                We are a passionate team of developers, designers, and digital strategists
                committed to transforming businesses through innovative technology solutions.
              </p>
              <div className="d-flex flex-wrap gap-3">
                <Link
                  to="/contact"
                  className="btn btn-light btn-lg rounded-pill px-4"
                >
                  <i className="fas fa-envelope me-2"></i>
                  Get In Touch
                </Link>
                <Link
                  to="/services"
                  className="btn btn-outline-light btn-lg rounded-pill px-4"
                >
                  <i className="fas fa-cogs me-2"></i>
                  Our Services
                </Link>
              </div>
            </div>
            <div className="col-lg-6">
              <div className="text-center">
                <div className="icon-circle mx-auto" style={{
                  width: '200px',
                  height: '200px',
                  background: 'rgba(255, 255, 255, 0.1)',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255, 255, 255, 0.2)'
                }}>
                  <i className="fas fa-rocket text-white" style={{ fontSize: '4rem' }}></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-5 bg-light">
        <div className="container">
          <div className="row">
            {stats.map((stat, index) => (
              <div key={index} className="col-6 col-lg-2 text-center mb-4">
                <div className="h2 fw-bold text-gradient mb-2">
                  {stat.number}
                </div>
                <div className="text-muted">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-5 bg-white">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-8 text-center">
              <h2 className="display-4 fw-bold mb-4 text-dark">Our <span className="text-gradient">Mission</span></h2>
              <p className="lead text-muted mb-5">
                To empower businesses with innovative technology solutions that drive growth,
                efficiency, and success in the digital age. We believe in creating lasting
                partnerships and delivering value that transforms the way our clients operate.
              </p>
            </div>
          </div>

          <div className="row g-4">
            {values.map((value, index) => (
              <div key={index} className="col-md-6 col-lg-3">
                <div className="card-modern h-100 text-center p-4">
                  <div className="icon-circle icon-circle-primary mx-auto mb-3">
                    <i className={`fas fa-${value.icon} fs-4`}></i>
                  </div>
                  <h5 className="fw-bold text-dark mb-3">{value.title}</h5>
                  <p className="text-muted mb-0">{value.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-5 bg-light">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-8 text-center">
              <h2 className="display-4 fw-bold mb-4 text-dark">Our Team</h2>
              <p className="lead text-muted mb-5">
                Meet the passionate professionals behind Maruti IT Zone. Our diverse team 
                brings together expertise in development, design, and digital strategy.
              </p>
            </div>
          </div>

          <div className="row justify-content-center">
            <div className="col-lg-8">
              <div className="card border-0 shadow-lg rounded-4">
                <div className="card-body p-5 text-center">
                  <i className="fas fa-users text-primary mb-4" style={{ fontSize: '4rem' }}></i>
                  <h3 className="fw-bold text-dark mb-3">Ready to Work With Us?</h3>
                  <p className="text-muted mb-4">
                    Let's discuss your project and see how we can help bring your vision to life.
                  </p>
                  <Link
                    to="/contact-us"
                    className="btn btn-primary btn-lg rounded-pill px-4"
                  >
                    <i className="fas fa-rocket me-2"></i>
                    Start Your Project
                    <i className="fas fa-arrow-right ms-2"></i>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-primary py-5 text-center text-white">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-8">
              <h2 className="display-4 fw-bold mb-4">Let's Build Something Amazing Together</h2>
              <p className="lead mb-4">
                Ready to transform your business with cutting-edge technology?
                Get in touch with us today and let's discuss your next project.
              </p>
              <div className="d-flex flex-wrap justify-content-center gap-3">
                <Link
                  to="/contact-us"
                  className="btn btn-light btn-lg rounded-pill px-4"
                >
                  <i className="fas fa-rocket me-2"></i>
                  Get Started Today
                </Link>
                <Link
                  to="/portfolio"
                  className="btn btn-outline-light btn-lg rounded-pill px-4"
                >
                  <i className="fas fa-eye me-2"></i>
                  View Our Work
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
