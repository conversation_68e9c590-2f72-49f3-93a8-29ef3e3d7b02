import { Link } from 'react-router-dom';

const Services = () => {
  const services = [
    {
      id: 1,
      title: 'Web Development',
      description: 'Custom websites and web applications built with modern technologies and best practices.',
      price: '₹50,000 - ₹5,00,000',
      duration: '4-12 weeks',
      popular: true,
      icon: 'code',
      features: [
        'Responsive Design',
        'SEO Optimization',
        'Fast Loading Speed',
        'Cross-browser Compatibility',
        'Content Management System',
        'Security Implementation'
      ]
    },
    {
      id: 2,
      title: 'Mobile App Development',
      description: 'Native and cross-platform mobile applications for iOS and Android devices.',
      price: '₹1,00,000 - ₹10,00,000',
      duration: '8-16 weeks',
      popular: false,
      icon: 'mobile-alt',
      features: [
        'Native Performance',
        'Push Notifications',
        'Offline Functionality',
        'App Store Optimization',
        'User Analytics',
        'Social Media Integration'
      ]
    },
    {
      id: 3,
      title: 'Cloud Solutions',
      description: 'Scalable cloud infrastructure and migration services for modern businesses.',
      price: '₹25,000 - ₹2,50,000',
      duration: '2-8 weeks',
      popular: false,
      icon: 'cloud',
      features: [
        'Cloud Migration',
        'Auto Scaling',
        'Backup Solutions',
        'Security Monitoring',
        'Cost Optimization',
        '24/7 Support'
      ]
    },
    {
      id: 4,
      title: 'E-commerce Solutions',
      description: 'Complete online store solutions with payment integration and inventory management.',
      price: '₹75,000 - ₹8,00,000',
      duration: '6-14 weeks',
      popular: true,
      icon: 'shopping-cart',
      features: [
        'Payment Gateway Integration',
        'Inventory Management',
        'Order Tracking',
        'Customer Management',
        'Analytics Dashboard',
        'Mobile Responsive'
      ]
    },
    {
      id: 5,
      title: 'UI/UX Design',
      description: 'User-centered design solutions that enhance user experience and engagement.',
      price: '₹30,000 - ₹2,00,000',
      duration: '3-8 weeks',
      popular: false,
      icon: 'palette',
      features: [
        'User Research',
        'Wireframing',
        'Prototyping',
        'Visual Design',
        'Usability Testing',
        'Design System'
      ]
    },
    {
      id: 6,
      title: 'Digital Marketing',
      description: 'Comprehensive digital marketing strategies to grow your online presence.',
      price: '₹15,000 - ₹1,50,000/month',
      duration: 'Ongoing',
      popular: false,
      icon: 'bullhorn',
      features: [
        'SEO Optimization',
        'Social Media Marketing',
        'Content Marketing',
        'PPC Advertising',
        'Email Marketing',
        'Analytics & Reporting'
      ]
    }
  ];

  return (
    <div className="services-page">
      {/* Hero Section */}
      <section className="bg-gradient-primary py-5 text-white text-center">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-8">
              <h1 className="display-3 fw-bold mb-4">
                Professional IT Solutions
              </h1>
              <p className="lead mb-4 opacity-90">
                We provide comprehensive technology services to help your business grow and succeed in the digital world.
                From web development to cloud solutions, we've got you covered.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-5 bg-light">
        <div className="container">
          <div className="row g-4">
            {services.map((service) => (
              <div key={service.id} className="col-md-6 col-lg-4">
                <div className="card-modern h-100 position-relative">
                  {service.popular && (
                    <span className="badge bg-gradient-primary position-absolute top-0 end-0 m-3" style={{ zIndex: 1 }}>
                      <i className="fas fa-star me-1"></i>
                      Popular
                    </span>
                  )}

                  <div className="p-4">
                    <div className="icon-circle icon-circle-primary mb-3">
                      <i className={`fas fa-${service.icon} fs-4`}></i>
                    </div>

                    <h3 className="h4 fw-bold mb-3 text-dark">
                      {service.title}
                    </h3>

                    <p className="text-muted mb-3">
                      {service.description}
                    </p>

                    <div className="mb-3">
                      <div className="d-flex justify-content-between align-items-center mb-2">
                        <span className="fw-semibold text-dark">Price Range:</span>
                        <span className="text-gradient fw-bold">{service.price}</span>
                      </div>
                      <div className="d-flex justify-content-between align-items-center">
                        <span className="fw-semibold text-dark">Duration:</span>
                        <span className="text-muted">{service.duration}</span>
                      </div>
                    </div>

                    <div className="mb-4">
                      <h5 className="fw-semibold mb-2 text-dark">
                        Key Features:
                      </h5>
                      <ul className="list-unstyled">
                        {service.features.slice(0, 4).map((feature, idx) => (
                          <li key={idx} className="d-flex align-items-center small mb-1">
                            <i className="fas fa-check text-success me-2 flex-shrink-0"></i>
                            <span className="text-muted">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <Link
                      to="/contact"
                      className="btn btn-brand-primary w-100"
                    >
                      Get Quote
                      <i className="fas fa-arrow-right ms-2"></i>
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-primary py-5 text-white text-center">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-8">
              <h2 className="display-4 fw-bold mb-4">
                Ready to Get Started?
              </h2>
              <p className="lead mb-4 opacity-90">
                Let's discuss your project requirements and create a custom solution that fits your needs and budget.
              </p>
              <div className="d-flex flex-wrap justify-content-center gap-3">
                <Link
                  to="/contact"
                  className="btn btn-light btn-lg rounded-pill px-4"
                >
                  <i className="fas fa-quote-left me-2"></i>
                  Get Free Quote
                </Link>
                <Link
                  to="/portfolio"
                  className="btn btn-outline-light btn-lg rounded-pill px-4"
                >
                  <i className="fas fa-eye me-2"></i>
                  View Our Work
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Services;
