import { useState } from 'react';
import { Container, Row, Col, Card, CardBody, CardHeader, Button, Table } from 'reactstrap';

const ManagePortfolio = () => {
  const [projects] = useState([
    {
      id: 1,
      title: 'E-Commerce Platform',
      category: 'Web Development',
      client: 'RetailCorp',
      image: '🛒',
      technologies: ['React', 'Node.js', 'MongoDB'],
      status: 'Completed',
      date: '2024-01-15'
    },
    {
      id: 2,
      title: 'Healthcare Mobile App',
      category: 'Mobile Development',
      client: 'MediCare Solutions',
      image: '🏥',
      technologies: ['React Native', 'Firebase'],
      status: 'Completed',
      date: '2024-01-10'
    },
    {
      id: 3,
      title: 'Cloud Migration Project',
      category: 'Cloud Solutions',
      client: 'TechStart Inc',
      image: '☁️',
      technologies: ['AWS', 'Docker', 'Kubernetes'],
      status: 'In Progress',
      date: '2024-01-20'
    },
    {
      id: 4,
      title: 'Digital Marketing Campaign',
      category: 'Marketing',
      client: 'GrowthCo',
      image: '📈',
      technologies: ['Google Ads', 'Analytics'],
      status: 'Completed',
      date: '2024-01-05'
    }
  ]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed': return 'success';
      case 'In Progress': return 'warning';
      case 'Planning': return 'info';
      default: return 'secondary';
    }
  };

  return (
    <div className="manage-portfolio-page">
      <Container fluid>
        <Row className="mb-4">
          <Col>
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <h2 className="mb-1">Manage Portfolio</h2>
                <p className="text-muted mb-0">Add, edit, or remove portfolio projects</p>
              </div>
              <Button color="primary" className="btn-primary-custom">
                ➕ Add New Project
              </Button>
            </div>
          </Col>
        </Row>

        <Row>
          <Col>
            <Card className="card-custom">
              <CardHeader className="bg-white">
                <h5 className="mb-0">Portfolio Projects</h5>
              </CardHeader>
              <CardBody className="p-0">
                <Table responsive hover className="mb-0">
                  <thead className="table-light">
                    <tr>
                      <th>Image</th>
                      <th>Project Title</th>
                      <th>Category</th>
                      <th>Client</th>
                      <th>Technologies</th>
                      <th>Status</th>
                      <th>Date</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {projects.map((project) => (
                      <tr key={project.id}>
                        <td>
                          <div 
                            style={{ 
                              fontSize: '2rem',
                              width: '50px',
                              height: '50px',
                              background: 'var(--gradient-primary)',
                              borderRadius: '8px',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              color: 'white'
                            }}
                          >
                            {project.image}
                          </div>
                        </td>
                        <td>
                          <strong>{project.title}</strong>
                        </td>
                        <td>
                          <span className="badge bg-info">{project.category}</span>
                        </td>
                        <td>{project.client}</td>
                        <td>
                          <div className="d-flex flex-wrap">
                            {project.technologies.map((tech, idx) => (
                              <span 
                                key={idx}
                                className="badge bg-light text-dark me-1 mb-1"
                                style={{ fontSize: '0.7rem' }}
                              >
                                {tech}
                              </span>
                            ))}
                          </div>
                        </td>
                        <td>
                          <span className={`badge bg-${getStatusColor(project.status)}`}>
                            {project.status}
                          </span>
                        </td>
                        <td>
                          <small className="text-muted">{project.date}</small>
                        </td>
                        <td>
                          <Button 
                            size="sm" 
                            color="warning" 
                            className="me-2"
                          >
                            ✏️ Edit
                          </Button>
                          <Button 
                            size="sm" 
                            color="danger"
                          >
                            🗑️ Delete
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </CardBody>
            </Card>
          </Col>
        </Row>

        {/* Portfolio Stats */}
        <Row className="mt-4">
          <Col lg={3} md={6} className="mb-3">
            <Card className="card-custom">
              <CardBody className="text-center">
                <h3 className="text-primary">150+</h3>
                <p className="mb-0">Total Projects</p>
              </CardBody>
            </Card>
          </Col>
          <Col lg={3} md={6} className="mb-3">
            <Card className="card-custom">
              <CardBody className="text-center">
                <h3 className="text-success">140</h3>
                <p className="mb-0">Completed</p>
              </CardBody>
            </Card>
          </Col>
          <Col lg={3} md={6} className="mb-3">
            <Card className="card-custom">
              <CardBody className="text-center">
                <h3 className="text-warning">8</h3>
                <p className="mb-0">In Progress</p>
              </CardBody>
            </Card>
          </Col>
          <Col lg={3} md={6} className="mb-3">
            <Card className="card-custom">
              <CardBody className="text-center">
                <h3 className="text-info">2</h3>
                <p className="mb-0">Planning</p>
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default ManagePortfolio;
