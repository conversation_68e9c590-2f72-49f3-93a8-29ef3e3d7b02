import React from 'react';
import { Contain<PERSON>, <PERSON>, Col, Card, CardBody, But<PERSON> } from 'reactstrap';
import { FaCode, FaMobile, FaCloud, FaUsers, FaRocket, FaArrowRight, FaCheck } from 'react-icons/fa';
import { Link } from 'react-router-dom';

const Home = () => {
  const services = [
    {
      icon: FaCode,
      title: 'Web Development',
      description: 'Modern, responsive websites built with latest technologies',
      color: '#5e72e4'
    },
    {
      icon: FaMobile,
      title: 'Mobile Apps',
      description: 'Native and cross-platform mobile applications',
      color: '#2dce89'
    },
    {
      icon: FaCloud,
      title: 'Cloud Solutions',
      description: 'Scalable cloud infrastructure and deployment',
      color: '#11cdef'
    },
    {
      icon: FaUsers,
      title: 'IT Consulting',
      description: 'Expert guidance for your technology decisions',
      color: '#fb6340'
    }
  ];

  const stats = [
    { number: '100+', label: 'Projects Completed' },
    { number: '50+', label: 'Happy Clients' },
    { number: '10+', label: 'Team Members' },
    { number: '5+', label: 'Years Experience' }
  ];

  const features = [
    'Professional Team',
    '24/7 Support',
    'Latest Technologies',
    'Competitive Pricing',
    'Quality Assurance',
    'On-time Delivery'
  ];

  return (
    <div className="home-page">
      {/* Hero Section */}
      <section 
        className="hero-section py-5"
        style={{
          background: 'linear-gradient(135deg, #5e72e4 0%, #2dce89 100%)',
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center'
        }}
      >
        <Container>
          <Row className="align-items-center">
            <Col lg={6}>
              <h1 className="display-3 fw-bold text-white mb-4">
                Welcome to <span style={{ color: '#fb6340' }}>M</span>
                <span style={{ color: '#11cdef' }}>i</span>
                <span style={{ color: '#fb6340' }}>Z</span>
              </h1>
              <h2 className="h3 text-white mb-4">
                Maruti IT Zone - Your Technology Partner
              </h2>
              <p className="lead text-white mb-4">
                We provide innovative IT solutions that help businesses grow and succeed 
                in the digital world. From web development to cloud solutions, we've got you covered.
              </p>
              <div className="d-flex flex-wrap gap-3">
                <Button
                  tag={Link}
                  to="/services"
                  size="lg"
                  className="btn-light rounded-pill px-4"
                >
                  Our Services
                  <FaArrowRight className="ms-2" />
                </Button>
                <Button
                  tag={Link}
                  to="/contact"
                  size="lg"
                  className="btn-outline-light rounded-pill px-4"
                >
                  Get Started
                </Button>
              </div>
            </Col>
            <Col lg={6}>
              <div className="text-center">
                <div 
                  className="bg-white bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-4"
                  style={{ width: '200px', height: '200px' }}
                >
                  <FaRocket size={80} className="text-white" />
                </div>
                <h3 className="text-white">Ready to Launch Your Project?</h3>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Services Section */}
      <section className="py-5 bg-light">
        <Container>
          <Row>
            <Col lg={8} className="mx-auto text-center mb-5">
              <h2 className="display-4 fw-bold text-dark mb-4">Our Services</h2>
              <p className="lead text-muted">
                We offer comprehensive IT solutions to help your business thrive in the digital age.
              </p>
            </Col>
          </Row>
          <Row>
            {services.map((service, index) => {
              const IconComponent = service.icon;
              return (
                <Col md={6} lg={3} key={index} className="mb-4">
                  <Card className="h-100 border-0 shadow-sm">
                    <CardBody className="text-center p-4">
                      <div 
                        className="rounded-circle d-inline-flex align-items-center justify-content-center mb-3"
                        style={{
                          width: '80px',
                          height: '80px',
                          backgroundColor: `${service.color}20`,
                          color: service.color
                        }}
                      >
                        <IconComponent size={40} />
                      </div>
                      <h5 className="fw-bold text-dark mb-3">{service.title}</h5>
                      <p className="text-muted mb-0">{service.description}</p>
                    </CardBody>
                  </Card>
                </Col>
              );
            })}
          </Row>
        </Container>
      </section>

      {/* Stats Section */}
      <section className="py-5 bg-white">
        <Container>
          <Row>
            {stats.map((stat, index) => (
              <Col md={6} lg={3} key={index} className="text-center mb-4">
                <div className="h1 fw-bold text-primary mb-2">{stat.number}</div>
                <div className="text-muted">{stat.label}</div>
              </Col>
            ))}
          </Row>
        </Container>
      </section>

      {/* Features Section */}
      <section className="py-5 bg-light">
        <Container>
          <Row className="align-items-center">
            <Col lg={6}>
              <h2 className="display-4 fw-bold text-dark mb-4">Why Choose MiZ?</h2>
              <p className="lead text-muted mb-4">
                We combine technical expertise with creative thinking to deliver 
                exceptional results that drive business growth.
              </p>
              <Row>
                {features.map((feature, index) => (
                  <Col sm={6} key={index} className="mb-3">
                    <div className="d-flex align-items-center">
                      <FaCheck className="text-success me-3" />
                      <span className="text-dark">{feature}</span>
                    </div>
                  </Col>
                ))}
              </Row>
            </Col>
            <Col lg={6}>
              <Card className="border-0 shadow-lg">
                <CardBody className="p-5 text-center">
                  <FaUsers size={60} className="text-primary mb-4" />
                  <h3 className="fw-bold text-dark mb-3">Ready to Get Started?</h3>
                  <p className="text-muted mb-4">
                    Let's discuss your project and see how we can help bring your vision to life.
                  </p>
                  <Button
                    tag={Link}
                    to="/contact-us"
                    color="primary"
                    size="lg"
                    className="rounded-pill"
                  >
                    Contact Us Today
                    <FaArrowRight className="ms-2" />
                  </Button>
                </CardBody>
              </Card>
            </Col>
          </Row>
        </Container>
      </section>

      {/* CTA Section */}
      <section 
        className="py-5 text-white text-center"
        style={{
          background: 'linear-gradient(135deg, #5e72e4 0%, #2dce89 100%)'
        }}
      >
        <Container>
          <Row className="justify-content-center">
            <Col lg={8}>
              <h2 className="display-4 fw-bold mb-4">Let's Build Something Amazing Together</h2>
              <p className="lead mb-4">
                Ready to transform your business with cutting-edge technology? 
                Get in touch with us today and let's discuss your next project.
              </p>
              <div className="d-flex flex-wrap justify-content-center gap-3">
                <Button
                  tag={Link}
                  to="/contact-us"
                  size="lg"
                  className="btn-light rounded-pill"
                >
                  Get Free Quote
                </Button>
                <Button
                  tag={Link}
                  to="/portfolio"
                  size="lg"
                  className="btn-outline-light rounded-pill"
                >
                  View Our Work
                </Button>
              </div>
            </Col>
          </Row>
        </Container>
      </section>
    </div>
  );
};

export default Home;
