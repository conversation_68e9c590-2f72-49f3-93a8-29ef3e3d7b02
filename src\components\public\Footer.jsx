import { Link } from 'react-router-dom';
import logoImage from '../../assets/img/logo.png';
import { COMPANY_INFO } from '../../assets/js/constants';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="footer-modern-new">
      {/* Main Footer Content */}
      <div className="footer-main">
        <div className="container">
          <div className="row g-5">
            {/* Company Info */}
            <div className="col-lg-4 col-md-6">
              <div className="footer-brand">
                <div className="brand-logo-section mb-4">
                  <img
                    src={logoImage}
                    alt="Maruti iT Zone"
                    className="footer-logo"
                  />
                  <div className="brand-text-section">
                    <h4 className="brand-name">Maruti iT Zone</h4>
                    <p className="brand-tagline">We Code Future...</p>
                  </div>
                </div>
                <p className="company-description">
                  Transforming businesses with innovative IT solutions. We deliver cutting-edge 
                  technology services to help you stay ahead in the digital world.
                </p>
                <div className="social-links">
                  <a href="#" className="social-link facebook" aria-label="Facebook">
                    <i className="fab fa-facebook-f"></i>
                  </a>
                  <a href="#" className="social-link twitter" aria-label="Twitter">
                    <i className="fab fa-twitter"></i>
                  </a>
                  <a href="#" className="social-link linkedin" aria-label="LinkedIn">
                    <i className="fab fa-linkedin-in"></i>
                  </a>
                  <a href="#" className="social-link instagram" aria-label="Instagram">
                    <i className="fab fa-instagram"></i>
                  </a>
                  <a href="#" className="social-link youtube" aria-label="YouTube">
                    <i className="fab fa-youtube"></i>
                  </a>
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div className="col-lg-2 col-md-6">
              <div className="footer-section">
                <h5 className="footer-title">Quick Links</h5>
                <ul className="footer-links">
                  {/* <li>
                    <Link to="/" className="footer-link">
                      <i className="fas fa-home me-2"></i>
                      Home
                    </Link>
                  </li> */}
                  <li>
                    <Link to="/about-us" className="footer-link">
                      <i className="fas fa-info-circle me-2"></i>
                      About-Us
                    </Link>
                  </li>
                   <li>
                    <Link to="/contact-us" className="footer-link">
                      <i className="fas fa-envelope me-2"></i>
                      Contact-Us
                    </Link>
                  </li>
                  <li>
                    <Link to="/services" className="footer-link">
                      <i className="fas fa-cogs me-2"></i>
                      Services
                    </Link>
                  </li>
                  <li>
                    <Link to="/portfolio" className="footer-link">
                      <i className="fas fa-briefcase me-2"></i>
                      Portfolio
                    </Link>
                  </li>
                  <li>
                    <Link to="/team" className="footer-link">
                      <i className="fas fa-users me-2"></i>
                      Our Team
                    </Link>
                  </li>
                  <li>
                    <Link to="/careers" className="footer-link">
                      <i className="fas fa-briefcase me-2"></i>
                      Careers
                    </Link>
                  </li>
                </ul>
              </div>
            </div>

            {/* Services */}
            <div className="col-lg-3 col-md-6">
              <div className="footer-section">
                <h5 className="footer-title">Our Services</h5>
                <ul className="footer-links">
                  <li>
                    <Link to="/services" className="footer-link">
                      <i className="fas fa-code me-2"></i>
                      Web Development
                    </Link>
                  </li>
                  <li>
                    <Link to="/services" className="footer-link">
                      <i className="fas fa-mobile-alt me-2"></i>
                      Mobile Apps
                    </Link>
                  </li>
                  <li>
                    <Link to="/services" className="footer-link">
                      <i className="fas fa-cloud me-2"></i>
                      Cloud Solutions
                    </Link>
                  </li>
                  <li>
                    <Link to="/services" className="footer-link">
                      <i className="fas fa-bullhorn me-2"></i>
                      Digital Marketing
                    </Link>
                  </li>
                  <li>
                    <Link to="/services" className="footer-link">
                      <i className="fas fa-paint-brush me-2"></i>
                      UI/UX Design
                    </Link>
                  </li>
                  <li>
                    <Link to="/services" className="footer-link">
                      <i className="fas fa-handshake me-2"></i>
                      IT Consulting
                    </Link>
                  </li>
                </ul>
              </div>
            </div>

            {/* Contact Info */}
            <div className="col-lg-3 col-md-6">
              <div className="footer-section">
                <h5 className="footer-title">Get In Touch</h5>
                <div className="contact-info">
                  <div className="contact-item">
                    <i className="fas fa-map-marker-alt contact-icon"></i>
                    <div className="contact-details">
                      <p>{COMPANY_INFO.address.street}, {COMPANY_INFO.address.city}</p>
                      <p>{COMPANY_INFO.address.state}, {COMPANY_INFO.address.country}</p>
                    </div>
                  </div>
                      <div className="contact-item">
                    <i className="fas fa-map-marker-alt contact-icon"></i>
                    <div className="contact-details">
                      <p> Near Raipura Hospital Mahadev Ghat Road Raipur C.G,India</p>
                    </div>
                  </div>
                  <div className="contact-item">
                    <i className="fas fa-phone contact-icon"></i>
                    <div className="contact-details">
                      <a href={`tel:${COMPANY_INFO.phone.replace(/[^+\d]/g, '')}`}>{COMPANY_INFO.phone}</a>
                    </div>
                  </div>
                  <div className="contact-item">
                    <i className="fas fa-envelope contact-icon"></i>
                    <div className="contact-details">
                      <a href={`mailto:${COMPANY_INFO.email}`}>{COMPANY_INFO.email}</a>
                    </div>
                  </div>
                  <div className="contact-item">
                    <i className="fas fa-clock contact-icon"></i>
                    <div className="contact-details">
                      <p>Mon - Fri: 9:00 AM - 6:00 PM</p>
                      <p>Saturday: 10:00 AM - 4:00 PM</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer Bottom */}
      <div className="footer-bottom">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-md-6">
              <p className="copyright">
                © {currentYear} <span className="text-gradient">Maruti iT Zone</span>. All rights reserved.
              </p>
            </div>
            <div className="col-md-6">
              <div className="footer-bottom-links">
                <Link to="/privacy-policy" className="footer-bottom-link">
                  Privacy Policy
                </Link>
                <Link to="/terms-of-service" className="footer-bottom-link">
                  Terms of Service
                </Link>
                <Link to="/sitemap" className="footer-bottom-link">
                  Sitemap
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Back to Top Button */}
      <button 
        className="back-to-top"
        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
        aria-label="Back to top"
      >
        <i className="fas fa-chevron-up"></i>
      </button>
    </footer>
  );
};

export default Footer;
