import { useState } from 'react';
import { Container, Row, Col, Card, CardBody, CardHeader, Button, Table, Modal, ModalHeader, ModalBody, Form, FormGroup, Label, Input } from 'reactstrap';

const ManageServices = () => {
  const [services, setServices] = useState([
    {
      id: 1,
      title: 'Web Development',
      description: 'Custom web applications built with modern technologies',
      icon: '💻',
      price: '$2000-$10000',
      status: 'Active'
    },
    {
      id: 2,
      title: 'Mobile App Development',
      description: 'Native and cross-platform mobile applications',
      icon: '📱',
      price: '$3000-$15000',
      status: 'Active'
    },
    {
      id: 3,
      title: 'Cloud Solutions',
      description: 'Scalable cloud infrastructure and migration services',
      icon: '☁️',
      price: '$1500-$8000',
      status: 'Active'
    },
    {
      id: 4,
      title: 'Digital Marketing',
      description: 'Comprehensive digital marketing strategies',
      icon: '🎯',
      price: '$500-$3000',
      status: 'Active'
    }
  ]);

  const [modal, setModal] = useState(false);
  const [editingService, setEditingService] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    icon: '',
    price: '',
    status: 'Active'
  });

  const toggleModal = () => {
    setModal(!modal);
    if (!modal) {
      setEditingService(null);
      setFormData({
        title: '',
        description: '',
        icon: '',
        price: '',
        status: 'Active'
      });
    }
  };

  const handleEdit = (service) => {
    setEditingService(service);
    setFormData(service);
    setModal(true);
  };

  const handleDelete = (id) => {
    if (window.confirm('Are you sure you want to delete this service?')) {
      setServices(services.filter(service => service.id !== id));
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (editingService) {
      // Update existing service
      setServices(services.map(service => 
        service.id === editingService.id 
          ? { ...formData, id: editingService.id }
          : service
      ));
    } else {
      // Add new service
      const newService = {
        ...formData,
        id: Math.max(...services.map(s => s.id)) + 1
      };
      setServices([...services, newService]);
    }
    
    toggleModal();
  };

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <div className="manage-services-page">
      <Container fluid>
        <Row className="mb-4">
          <Col>
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <h2 className="mb-1">Manage Services</h2>
                <p className="text-muted mb-0">Add, edit, or remove services offered by your company</p>
              </div>
              <Button color="primary" className="btn-primary-custom" onClick={toggleModal}>
                ➕ Add New Service
              </Button>
            </div>
          </Col>
        </Row>

        <Row>
          <Col>
            <Card className="card-custom">
              <CardHeader className="bg-white">
                <h5 className="mb-0">Services List</h5>
              </CardHeader>
              <CardBody className="p-0">
                <Table responsive hover className="mb-0">
                  <thead className="table-light">
                    <tr>
                      <th>Icon</th>
                      <th>Service Title</th>
                      <th>Description</th>
                      <th>Price Range</th>
                      <th>Status</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {services.map((service) => (
                      <tr key={service.id}>
                        <td>
                          <div style={{ fontSize: '2rem' }}>{service.icon}</div>
                        </td>
                        <td>
                          <strong>{service.title}</strong>
                        </td>
                        <td>
                          <div style={{ maxWidth: '300px' }}>
                            {service.description}
                          </div>
                        </td>
                        <td>
                          <span className="badge bg-info">{service.price}</span>
                        </td>
                        <td>
                          <span 
                            className={`badge ${service.status === 'Active' ? 'bg-success' : 'bg-secondary'}`}
                          >
                            {service.status}
                          </span>
                        </td>
                        <td>
                          <Button 
                            size="sm" 
                            color="warning" 
                            className="me-2"
                            onClick={() => handleEdit(service)}
                          >
                            ✏️ Edit
                          </Button>
                          <Button 
                            size="sm" 
                            color="danger"
                            onClick={() => handleDelete(service.id)}
                          >
                            🗑️ Delete
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </CardBody>
            </Card>
          </Col>
        </Row>

        {/* Add/Edit Service Modal */}
        <Modal isOpen={modal} toggle={toggleModal} size="lg">
          <ModalHeader toggle={toggleModal}>
            {editingService ? 'Edit Service' : 'Add New Service'}
          </ModalHeader>
          <ModalBody>
            <Form onSubmit={handleSubmit}>
              <Row>
                <Col md={6}>
                  <FormGroup>
                    <Label for="title">Service Title *</Label>
                    <Input
                      type="text"
                      name="title"
                      id="title"
                      value={formData.title}
                      onChange={handleChange}
                      required
                      placeholder="Enter service title"
                    />
                  </FormGroup>
                </Col>
                <Col md={6}>
                  <FormGroup>
                    <Label for="icon">Icon (Emoji)</Label>
                    <Input
                      type="text"
                      name="icon"
                      id="icon"
                      value={formData.icon}
                      onChange={handleChange}
                      placeholder="e.g., 💻"
                    />
                  </FormGroup>
                </Col>
              </Row>
              
              <FormGroup>
                <Label for="description">Description *</Label>
                <Input
                  type="textarea"
                  name="description"
                  id="description"
                  rows="3"
                  value={formData.description}
                  onChange={handleChange}
                  required
                  placeholder="Enter service description"
                />
              </FormGroup>
              
              <Row>
                <Col md={6}>
                  <FormGroup>
                    <Label for="price">Price Range</Label>
                    <Input
                      type="text"
                      name="price"
                      id="price"
                      value={formData.price}
                      onChange={handleChange}
                      placeholder="e.g., $1000-$5000"
                    />
                  </FormGroup>
                </Col>
                <Col md={6}>
                  <FormGroup>
                    <Label for="status">Status</Label>
                    <Input
                      type="select"
                      name="status"
                      id="status"
                      value={formData.status}
                      onChange={handleChange}
                    >
                      <option value="Active">Active</option>
                      <option value="Inactive">Inactive</option>
                    </Input>
                  </FormGroup>
                </Col>
              </Row>
              
              <div className="text-end">
                <Button type="button" color="secondary" className="me-2" onClick={toggleModal}>
                  Cancel
                </Button>
                <Button type="submit" className="btn-primary-custom">
                  {editingService ? 'Update Service' : 'Add Service'}
                </Button>
              </div>
            </Form>
          </ModalBody>
        </Modal>
      </Container>
    </div>
  );
};

export default ManageServices;
