// Constants for Maruti IT Zone website

// Company Information
export const COMPANY_INFO = {
  name: 'Maruti IT Zone',
  tagline: 'Your Partner in Digital Transformation',
  email: import.meta.env.VITE_COMPANY_EMAIL || '<EMAIL>',
  phone: import.meta.env.VITE_COMPANY_PHONE_PRIMARY || '+91-7587167344',
  alternatePhone: import.meta.env.VITE_COMPANY_PHONE_SECONDARY || '+91-7470929113',
  emergencyPhone: '+91 9691490868',
  address: {
    street: 'Aama Talab Road',
    city: 'Dhamtari',
    state: 'Raipur C.G.',
    zipCode: '493773',
    country: 'India',
    fullAddress: import.meta.env.VITE_COMPANY_ADDRESS || 'Aama Talab Road, Dhamtari, Raipur C.G. - 493773'
  },
  website: 'https://marutiitzone.com',
  founded: '2019',
  employees: '15+',
  projectsCompleted: '150+',
  clientsServed: '50+',
  description: 'Maruti iT Zone is an IT company that specializes in providing a range of technology services and solutions. They focus on Web & Software Development, and Digital Marketing to help businesses.'
};

// Social Media Links
export const SOCIAL_LINKS = {
  facebook: 'https://facebook.com/marutiitzone',
  twitter: 'https://twitter.com/marutiitzone',
  linkedin: 'https://linkedin.com/company/marutiitzone',
  instagram: 'https://instagram.com/marutiitzone',
  youtube: 'https://youtube.com/marutiitzone',
  github: 'https://github.com/marutiitzone'
};

// Navigation Menu Items
export const NAVIGATION_ITEMS = [
  { path: '/', label: 'Home', icon: '🏠' },
  { path: '/about-us', label: 'About Us', icon: 'ℹ️' },
  { path: '/services', label: 'Services', icon: '⚙️' },
  { path: '/portfolio', label: 'Portfolio', icon: '💼' },
  { path: '/team', label: 'Our Team', icon: '👥' },
  { path: '/careers', label: 'Careers', icon: '💼' },
  { path: '/contact-us', label: 'Contact Us', icon: '📞' }
];

// Authorized Navigation Items
export const AUTHORIZED_NAVIGATION_ITEMS = [
  { path: '/authorized', label: 'Dashboard', icon: '📊' },
  { path: '/authorized/services', label: 'Manage Services', icon: '⚙️' },
  { path: '/authorized/portfolio', label: 'Manage Portfolio', icon: '💼' },
  { path: '/authorized/team', label: 'Manage Team', icon: '👥' },
  { path: '/authorized/settings', label: 'Settings', icon: '🔧' }
];

// Services Data (Based on actual Maruti IT Zone services)
export const SERVICES = [
  {
    id: 1,
    title: 'Web Development',
    description: 'We create dynamic, responsive websites to enhance your online presence and engage your audience effectively.',
    icon: '💻',
    features: ['Responsive Design', 'SEO Optimized', 'Fast Loading', 'Cross-browser Compatible'],
    technologies: ['React', 'Node.js', 'MongoDB', 'Express.js', 'PHP', 'WordPress'],
    priceRange: '₹15,000-₹2,50,000',
    duration: '2-12 weeks'
  },
  {
    id: 2,
    title: 'App Development',
    description: 'We build user-friendly mobile apps that offer seamless performance and keep your customers engaged.',
    icon: '📱',
    features: ['Native Performance', 'Cross-platform', 'App Store Optimization', 'Push Notifications'],
    technologies: ['React Native', 'Flutter', 'Swift', 'Kotlin', 'Java'],
    priceRange: '₹25,000-₹5,00,000',
    duration: '4-16 weeks'
  },
  {
    id: 3,
    title: 'Digital Marketing',
    description: 'Our digital marketing strategies drive traffic, boost visibility, and deliver results that matter.',
    icon: '🎯',
    features: ['SEO/SEM', 'Social Media Marketing', 'Content Marketing', 'Analytics'],
    technologies: ['Google Ads', 'Facebook Ads', 'Google Analytics', 'SEMrush'],
    priceRange: '₹5,000-₹50,000',
    duration: 'Ongoing'
  },
  {
    id: 4,
    title: 'Graphic Designing',
    description: 'We provide creative design solutions that visually communicate your brand\'s message effectively.',
    icon: '🎨',
    features: ['Brand Identity', 'Print Design', 'Digital Graphics', 'UI/UX Design'],
    technologies: ['Adobe Photoshop', 'Illustrator', 'Figma', 'Canva'],
    priceRange: '₹2,000-₹25,000',
    duration: '1-4 weeks'
  },
  {
    id: 5,
    title: 'IT Consulting',
    description: 'Our expert consultants provide strategic advice to optimize your IT operations and maximize efficiency.',
    icon: '💼',
    features: ['Strategic Planning', 'Technology Assessment', 'Process Optimization', 'Digital Transformation'],
    technologies: ['Cloud Computing', 'Enterprise Software', 'Security Solutions'],
    priceRange: '₹10,000-₹1,00,000',
    duration: '2-8 weeks'
  },
  {
    id: 6,
    title: 'Cybersecurity Solutions',
    description: 'Our cybersecurity solutions safeguard your business from digital threats and protect your data.',
    icon: '🛡️',
    features: ['Threat Assessment', 'Security Audits', 'Data Protection', '24/7 Monitoring'],
    technologies: ['Firewall', 'VPN', 'Encryption', 'SIEM'],
    priceRange: '₹15,000-₹1,50,000',
    duration: '2-6 weeks'
  },
  {
    id: 7,
    title: 'Cloud Solutions',
    description: 'Our cloud computing services ensure flexibility, scalability, and security for your business operations.',
    icon: '☁️',
    features: ['Auto Scaling', 'High Availability', 'Cost Optimization', 'Security'],
    technologies: ['AWS', 'Azure', 'Google Cloud', 'Docker'],
    priceRange: '₹20,000-₹3,00,000',
    duration: '3-10 weeks'
  },
  {
    id: 8,
    title: 'Logo Making',
    description: 'We design creative and professional logos that effectively represent your brand and its values.',
    icon: '🏷️',
    features: ['Brand Identity', 'Vector Graphics', 'Multiple Formats', 'Unlimited Revisions'],
    technologies: ['Adobe Illustrator', 'Photoshop', 'CorelDRAW'],
    priceRange: '₹1,500-₹15,000',
    duration: '3-7 days'
  }
];

// Technology Stack
export const TECHNOLOGIES = {
  frontend: [
    { name: 'React', icon: '⚛️', category: 'Frontend' },
    { name: 'Vue.js', icon: '🟢', category: 'Frontend' },
    { name: 'Angular', icon: '🔺', category: 'Frontend' },
    { name: 'HTML5', icon: '🌐', category: 'Frontend' },
    { name: 'CSS3', icon: '🎨', category: 'Frontend' },
    { name: 'JavaScript', icon: '📜', category: 'Frontend' }
  ],
  backend: [
    { name: 'Node.js', icon: '🟢', category: 'Backend' },
    { name: 'Python', icon: '🐍', category: 'Backend' },
    { name: 'Java', icon: '☕', category: 'Backend' },
    { name: 'PHP', icon: '🐘', category: 'Backend' },
    { name: 'C#', icon: '🔷', category: 'Backend' },
    { name: 'Go', icon: '🐹', category: 'Backend' }
  ],
  database: [
    { name: 'MongoDB', icon: '🍃', category: 'Database' },
    { name: 'MySQL', icon: '🐬', category: 'Database' },
    { name: 'PostgreSQL', icon: '🐘', category: 'Database' },
    { name: 'Redis', icon: '🔴', category: 'Database' },
    { name: 'Firebase', icon: '🔥', category: 'Database' }
  ],
  cloud: [
    { name: 'AWS', icon: '☁️', category: 'Cloud' },
    { name: 'Azure', icon: '🔵', category: 'Cloud' },
    { name: 'Google Cloud', icon: '🌤️', category: 'Cloud' },
    { name: 'Docker', icon: '🐳', category: 'Cloud' },
    { name: 'Kubernetes', icon: '⚙️', category: 'Cloud' }
  ],
  mobile: [
    { name: 'React Native', icon: '📱', category: 'Mobile' },
    { name: 'Flutter', icon: '🦋', category: 'Mobile' },
    { name: 'Swift', icon: '🍎', category: 'Mobile' },
    { name: 'Kotlin', icon: '🤖', category: 'Mobile' }
  ]
};

// Team Members
export const TEAM_MEMBERS = [
  {
    id: 1,
    name: 'Rajesh Kumar',
    position: 'CEO & Founder',
    department: 'Management',
    email: '<EMAIL>',
    phone: '+91 98765 43210',
    avatar: '👨‍💼',
    bio: 'Visionary leader with 15+ years of experience in IT industry leadership and business strategy.',
    skills: ['Strategic Planning', 'Business Development', 'Team Leadership', 'Client Relations'],
    joinDate: '2019-01-01',
    social: {
      linkedin: 'https://linkedin.com/in/rajeshkumar',
      twitter: 'https://twitter.com/rajeshkumar'
    }
  },
  {
    id: 2,
    name: 'Priya Sharma',
    position: 'Chief Technology Officer',
    department: 'Technology',
    email: '<EMAIL>',
    phone: '+91 98765 43211',
    avatar: '👩‍💻',
    bio: 'Technology expert with deep knowledge in software architecture and emerging technologies.',
    skills: ['Software Architecture', 'Cloud Computing', 'AI/ML', 'DevOps'],
    joinDate: '2019-02-15',
    social: {
      linkedin: 'https://linkedin.com/in/priyasharma',
      github: 'https://github.com/priyasharma'
    }
  },
  {
    id: 3,
    name: 'Amit Patel',
    position: 'Head of Operations',
    department: 'Operations',
    email: '<EMAIL>',
    phone: '+91 98765 43212',
    avatar: '👨‍💼',
    bio: 'Operations specialist ensuring smooth project delivery and client satisfaction.',
    skills: ['Project Management', 'Quality Assurance', 'Process Optimization', 'Client Success'],
    joinDate: '2019-03-01',
    social: {
      linkedin: 'https://linkedin.com/in/amitpatel'
    }
  }
];

// Project Categories
export const PROJECT_CATEGORIES = [
  { key: 'all', label: 'All Projects' },
  { key: 'web', label: 'Web Development' },
  { key: 'mobile', label: 'Mobile Apps' },
  { key: 'cloud', label: 'Cloud Solutions' },
  { key: 'marketing', label: 'Digital Marketing' },
  { key: 'ai', label: 'AI & ML' },
  { key: 'security', label: 'Cybersecurity' }
];

// Contact Form Fields
export const CONTACT_FORM_FIELDS = [
  { name: 'name', label: 'Full Name', type: 'text', required: true },
  { name: 'email', label: 'Email Address', type: 'email', required: true },
  { name: 'phone', label: 'Phone Number', type: 'tel', required: false },
  { name: 'company', label: 'Company Name', type: 'text', required: false },
  { name: 'subject', label: 'Subject', type: 'select', required: false },
  { name: 'message', label: 'Message', type: 'textarea', required: true }
];

// API Endpoints
export const API_ENDPOINTS = {
  auth: {
    login: '/auth/login',
    logout: '/auth/logout',
    refresh: '/auth/refresh'
  },
  services: {
    list: '/services',
    create: '/services',
    update: '/services/:id',
    delete: '/services/:id'
  },
  portfolio: {
    list: '/portfolio',
    create: '/portfolio',
    update: '/portfolio/:id',
    delete: '/portfolio/:id'
  },
  team: {
    list: '/team',
    create: '/team',
    update: '/team/:id',
    delete: '/team/:id'
  },
  contact: {
    submit: '/contact',
    list: '/contact'
  }
};

// Error Messages
export const ERROR_MESSAGES = {
  required: 'This field is required',
  email: 'Please enter a valid email address',
  phone: 'Please enter a valid phone number',
  minLength: 'Minimum length is {min} characters',
  maxLength: 'Maximum length is {max} characters',
  network: 'Network error. Please check your connection.',
  server: 'Server error. Please try again later.',
  unauthorized: 'You are not authorized to perform this action.',
  notFound: 'The requested resource was not found.'
};

// Success Messages
export const SUCCESS_MESSAGES = {
  login: 'Login successful!',
  logout: 'Logout successful!',
  save: 'Data saved successfully!',
  update: 'Data updated successfully!',
  delete: 'Data deleted successfully!',
  contact: 'Your message has been sent successfully!',
  copy: 'Copied to clipboard!'
};

// Theme Colors (CSS Custom Properties)
export const THEME_COLORS = {
  primary: {
    orange: '#FF5722',
    blue: '#2196F3'
  },
  secondary: {
    darkBlue: '#1976D2',
    lightOrange: '#FF7043'
  },
  neutral: {
    white: '#FFFFFF',
    lightGray: '#F5F5F5',
    darkGray: '#333333',
    textDark: '#212529',
    textLight: '#6C757D'
  }
};
