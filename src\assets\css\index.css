/* Bootstrap Import */
@import 'bootstrap/dist/css/bootstrap.min.css';

/* Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

/* Custom CSS Variables with your specified color scheme */
:root {
  --blue: #5e72e4;
  --indigo: #5603ad;
  --purple: #8965e0;
  --pink: #f3a4b5;
  --red: #f5365c;
  --orange: #fb6340;
  --yellow: #ffd600;
  --green: #2dce89;
  --teal: #11cdef;
  --cyan: #2bffc6;
  --white: #fff;
  --gray: #8898aa;
  --gray-dark: #32325d;
  --light: #ced4da;
  --lighter: #e9ecef;
  --primary: #5e72e4;
  --secondary: #f7fafc;
  --success: #2dce89;
  --info: #11cdef;
  --warning: #fb6340;
  --danger: #f5365c;
  --light: #adb5bd;
  --dark: #212529;
  --default: #172b4d;
  --white: #fff;
  --neutral: #fff;
  --darker: #000;

  /* Bootstrap variable overrides */
  --bs-primary: #5e72e4;
  --bs-secondary: #f7fafc;
  --bs-success: #2dce89;
  --bs-info: #11cdef;
  --bs-warning: #fb6340;
  --bs-danger: #f5365c;
  --bs-light: #adb5bd;
  --bs-dark: #212529;
}

/* Base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  color: var(--default);
}

/* Modern gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--blue) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--teal) 0%, var(--cyan) 100%);
}

.gradient-success {
  background: linear-gradient(135deg, var(--success) 0%, var(--green) 100%);
}

.gradient-overlay {
  background: linear-gradient(135deg, rgba(94, 114, 228, 0.9) 0%, rgba(17, 205, 239, 0.9) 100%);
}

/* Glass morphism effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-dark {
  background: rgba(33, 37, 41, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Modern card styles */
.modern-card {
  background: var(--white);
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  color: var(--bs-body-color) !important;
  background-color: var(--bs-body-bg) !important;
}

.modern-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.modern-card-dark {
  background: var(--gray-dark);
  color: var(--white);
}

/* Button styles */
.btn-modern {
  padding: 12px 30px;
  border-radius: 50px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  position: relative;
  overflow: hidden;
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-modern:hover::before {
  left: 100%;
}

.btn-primary-modern {
  background: linear-gradient(135deg, var(--primary), var(--blue));
  color: var(--white);
}

.btn-primary-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(94, 114, 228, 0.4);
  color: var(--white);
}

.btn-outline-modern {
  background: transparent;
  border: 2px solid var(--primary);
  color: var(--primary);
}

.btn-outline-modern:hover {
  background: var(--primary);
  color: var(--white);
  transform: translateY(-2px);
}

/* Hero section styles */
.hero-section {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--primary) 0%, var(--indigo) 50%, var(--purple) 100%);
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

/* Floating animations */
.float-animation {
  animation: float 6s ease-in-out infinite;
}

.float-delay-1 {
  animation-delay: -1s;
}

.float-delay-2 {
  animation-delay: -2s;
}

.float-delay-3 {
  animation-delay: -3s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Pulse animation */
.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Fade in animations */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeIn 0.8s ease-out forwards;
}

.fade-in-delay-1 {
  animation-delay: 0.2s;
}

.fade-in-delay-2 {
  animation-delay: 0.4s;
}

.fade-in-delay-3 {
  animation-delay: 0.6s;
}

@keyframes fadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Section spacing */
.section-padding {
  padding: 100px 0;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--lighter);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--blue);
}

/* Responsive utilities */
@media (max-width: 768px) {
  .section-padding {
    padding: 60px 0;
  }

  .hero-section {
    min-height: 80vh;
  }
}

/* Focus styles for accessibility */
button:focus,
a:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}



/* Additional modern styles */
.text-gradient {
  background: linear-gradient(135deg, var(--primary), var(--teal));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.icon-box {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.icon-box:hover {
  transform: scale(1.1) rotate(5deg);
}

/* Navbar styles */
.navbar-modern {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.navbar-scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

/* Footer styles */
.footer-modern {
  background: linear-gradient(135deg, var(--dark) 0%, var(--gray-dark) 100%);
  color: var(--white);
}

/* Loading animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

.loading-pulse {
  animation: pulse 2s infinite;
}

.loading-dot {
  animation: bounce 1.4s infinite ease-in-out both;
}

.loading-dot-1 {
  animation-delay: 0s;
}

.loading-dot-2 {
  animation-delay: 0.16s;
}

.loading-dot-3 {
  animation-delay: 0.32s;
}
