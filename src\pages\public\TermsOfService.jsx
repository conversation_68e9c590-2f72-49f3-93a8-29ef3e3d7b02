import { Link } from 'react-router-dom';
import SEO from '../../components/common/SEO';

const TermsOfService = () => {
  return (
    <div className="terms-of-service-page">
      <SEO
        title="Terms of Service - Maruti iT Zone"
        description="Read the terms and conditions for using Maruti iT Zone services. Understand your rights and responsibilities."
        keywords="terms of service, terms and conditions, legal, Maruti iT Zone"
        url="https://marutiitzone.com/terms-of-service"
      />

      {/* Hero Section */}
      <section className="bg-gradient-primary py-5 text-white">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-8 mx-auto text-center">
              <div className="hero-icon mb-4">
                <i className="fas fa-file-contract" style={{ fontSize: '4rem', color: 'rgba(255,255,255,0.9)' }}></i>
              </div>
              <h1 className="display-3 fw-bold text-white mb-4">
                Terms of Service
              </h1>
              <p className="lead text-white mb-4 opacity-90">
                Read the terms and conditions for using Maruti iT Zone services. Understand your rights and responsibilities.
              </p>
              <div className="d-flex flex-wrap justify-content-center gap-3">
                <Link to="/" className="btn btn-light btn-lg rounded-pill px-4">
                  <i className="fas fa-home me-2"></i>
                  Back to Home
                </Link>
                <Link to="/contact" className="btn btn-outline-light btn-lg rounded-pill px-4">
                  <i className="fas fa-envelope me-2"></i>
                  Contact Us
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="section-padding bg-light">
        <Container>
          <Row className="justify-content-center">
            <Col lg={10}>
              <Card
                className="shadow-lg border-0"
                style={{
                  backgroundColor: 'white',
                  borderRadius: '1rem'
                }}
              >
                <CardBody className="p-5">
                  <div className="mb-5">
                    <h2 className="h3 fw-bold mb-3 d-flex align-items-center">
                      <FaHandshake className="me-3 text-primary" />
                      Agreement to Terms
                    </h2>
                    <p className="mb-4">
                      By accessing and using the services provided by Maruti IT Zone, you accept and agree to be bound by the terms and provision of this agreement.
                    </p>
                  </div>

                  <div className="mb-5">
                    <h3 className="h4 fw-bold mb-3 d-flex align-items-center">
                      <FaGavel className="me-3 text-primary" />
                      Use License
                    </h3>
                    <p className="mb-3">
                      Permission is granted to temporarily download one copy of the materials on Maruti IT Zone's website for personal, non-commercial transitory viewing only.
                    </p>
                    <ul className="list-unstyled">
                      <li className="mb-2">• Modify or copy the materials</li>
                      <li className="mb-2">• Use the materials for any commercial purpose</li>
                      <li className="mb-2">• Attempt to decompile or reverse engineer any software</li>
                      <li className="mb-2">• Remove any copyright or other proprietary notations</li>
                    </ul>
                  </div>

                  <div className="mb-5">
                    <h3 className="h4 fw-bold mb-3 d-flex align-items-center">
                      <i className="fas fa-shield-alt me-3 text-primary"></i>
                      Disclaimer
                    </h3>
                    <p className="mb-3">
                      The materials on Maruti IT Zone's website are provided on an 'as is' basis. Maruti IT Zone makes no warranties, expressed or implied, and hereby disclaims and negates all other warranties including without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.
                    </p>
                  </div>

                  <div className="mb-5">
                    <h3 className="h4 fw-bold mb-3">Service Terms</h3>
                    <p className="mb-3">
                      Our IT services are provided subject to the following terms:
                    </p>
                    <ul className="list-unstyled">
                      <li className="mb-2">• Services will be delivered as per agreed specifications</li>
                      <li className="mb-2">• Payment terms as outlined in service agreements</li>
                      <li className="mb-2">• Intellectual property rights remain with respective owners</li>
                      <li className="mb-2">• Confidentiality of client data is maintained</li>
                    </ul>
                  </div>

                  <div className="mb-5">
                    <h3 className="h4 fw-bold mb-3">Limitations</h3>
                    <p className="mb-3">
                      In no event shall Maruti IT Zone or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use the materials on Maruti IT Zone's website, even if Maruti IT Zone or a Maruti IT Zone authorized representative has been notified orally or in writing of the possibility of such damage.
                    </p>
                  </div>

                  <div className="mb-5">
                    <h3 className="h4 fw-bold mb-3">Revisions and Errata</h3>
                    <p className="mb-3">
                      The materials appearing on Maruti IT Zone's website could include technical, typographical, or photographic errors. Maruti IT Zone does not warrant that any of the materials on its website are accurate, complete, or current. Maruti IT Zone may make changes to the materials contained on its website at any time without notice.
                    </p>
                  </div>

                  <div className="mb-5">
                    <h3 className="h4 fw-bold mb-3">Governing Law</h3>
                    <p className="mb-3">
                      These terms and conditions are governed by and construed in accordance with the laws of India and you irrevocably submit to the exclusive jurisdiction of the courts in that State or location.
                    </p>
                  </div>

                  <div className="text-center pt-4 border-top">
                    <p className="text-muted mb-3">
                      <strong>Last Updated:</strong> January 2024
                    </p>
                    <p className="text-muted mb-4">
                      If you have any questions about these Terms of Service, please contact us.
                    </p>
                    
                    <div className="d-flex flex-wrap justify-content-center gap-3">
                      <Button
                        tag={Link}
                        to="/contact-us"
                        color="primary"
                      >
                        <FaEnvelope className="me-2" />
                        Contact Us
                      </Button>
                      <Button
                        tag={Link}
                        to="/privacy-policy"
                        color="outline-primary"
                      >
                        Privacy Policy
                      </Button>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>
          </Row>
        </Container>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-primary py-5 text-center text-white">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-8">
              <h2 className="display-5 fw-bold mb-4">Need Legal Assistance?</h2>
              <p className="lead mb-4 opacity-90">
                If you have any questions about our terms of service or need clarification on any legal matters, our team is here to help.
              </p>
              <div className="d-flex flex-wrap justify-content-center gap-3">
                <Link
                  to="/contact-us"
                  className="btn btn-light btn-lg rounded-pill px-4"
                >
                  <i className="fas fa-phone me-2"></i>
                  Contact Legal Team
                </Link>
                <Link
                  to="/"
                  className="btn btn-outline-light btn-lg rounded-pill px-4"
                >
                  <i className="fas fa-home me-2"></i>
                  Back to Home
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default TermsOfService;
