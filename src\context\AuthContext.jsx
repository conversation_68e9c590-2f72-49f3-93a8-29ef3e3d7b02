import { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is logged in on app start
    const token = localStorage.getItem('authToken');
    const userData = localStorage.getItem('user');

    if (token && userData) {
      try {
        const parsedUser = JSON.parse(userData);
        setIsAuthenticated(true);
        setUser(parsedUser);
        // Token is already stored in localStorage
      } catch (error) {
        console.error('Error parsing user data:', error);
        localStorage.removeItem('authToken');
        localStorage.removeItem('user');
      }
    }
    setLoading(false);
  }, []);

  // Direct API call for login
  const loginAPI = async (credentials) => {
    const baseURL = import.meta.env.VITE_API_BASE_URL || 'https://jsonplaceholder.typicode.com';

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Dummy authentication logic
      if (credentials.email && credentials.password) {
        const userData = {
          id: credentials.email === '<EMAIL>' ? 1 : 2,
          name: credentials.email === '<EMAIL>' ? 'Authorized User' : 'John Doe',
          email: credentials.email,
          role: credentials.email === '<EMAIL>' ? 'authorized' : 'user',
          avatar: 'https://via.placeholder.com/150'
        };

        const token = 'dummy_jwt_token_' + Date.now();
        localStorage.setItem('authToken', token);

        return {
          success: true,
          data: { user: userData, token }
        };
      } else {
        return { success: false, error: 'Invalid credentials' };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: error.message };
    }
  };

  const login = async (credentials) => {
    try {
      setLoading(true);
      const response = await loginAPI(credentials);

      if (response.success) {
        const { user: userData } = response.data;
        setUser(userData);
        setIsAuthenticated(true);
        localStorage.setItem('user', JSON.stringify(userData));
        setLoading(false);
        return { success: true, user: userData };
      } else {
        setLoading(false);
        return { success: false, error: response.error };
      }
    } catch (error) {
      console.error('Login wrapper error:', error);
      setLoading(false);
      return { success: false, error: error.message };
    }
  };

  // Direct API call for register
  const registerAPI = async (userData) => {
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Dummy registration logic
      if (userData.name && userData.email && userData.password) {
        const newUser = {
          id: Date.now(),
          name: userData.name,
          email: userData.email,
          role: 'user',
          avatar: 'https://via.placeholder.com/150'
        };

        const token = 'dummy_jwt_token_' + Date.now();
        localStorage.setItem('authToken', token);

        return {
          success: true,
          data: { user: newUser, token }
        };
      } else {
        return { success: false, error: 'Invalid user data' };
      }
    } catch (error) {
      console.error('Register error:', error);
      return { success: false, error: error.message };
    }
  };

  const register = async (userData) => {
    try {
      setLoading(true);
      const response = await registerAPI(userData);

      if (response.success) {
        const { user: newUser } = response.data;
        setUser(newUser);
        setIsAuthenticated(true);
        localStorage.setItem('user', JSON.stringify(newUser));
        setLoading(false);
        return { success: true, user: newUser };
      } else {
        setLoading(false);
        return { success: false, error: response.error };
      }
    } catch (error) {
      console.error('Register wrapper error:', error);
      setLoading(false);
      return { success: false, error: error.message };
    }
  };

  const logout = async () => {
    try {
      // Simulate API logout call
      await new Promise(resolve => setTimeout(resolve, 500));

      setUser(null);
      setIsAuthenticated(false);
      localStorage.removeItem('user');
      localStorage.removeItem('authToken');
    } catch (error) {
      console.error('Logout error:', error);
      // Still logout locally even if API call fails
      setUser(null);
      setIsAuthenticated(false);
      localStorage.removeItem('user');
      localStorage.removeItem('authToken');
    }
  };

  const value = {
    user,
    isAuthenticated,
    loading,
    login,
    register,
    logout
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
