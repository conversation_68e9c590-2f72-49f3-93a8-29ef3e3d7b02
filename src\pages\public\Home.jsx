// Home page component
import { <PERSON> } from 'react-router-dom';
import HeroSection from '../../components/public/HeroSection';

const Home = () => {
  const services = [
    {
      icon: 'code',
      title: 'Web Development',
      description: 'Modern, responsive websites built with latest technologies'
    },
    {
      icon: 'mobile-alt',
      title: 'Mobile Apps',
      description: 'Native and cross-platform mobile applications'
    },
    {
      icon: 'cloud',
      title: 'Cloud Solutions',
      description: 'Scalable cloud infrastructure and deployment'
    },
    {
      icon: 'users',
      title: 'IT Consulting',
      description: 'Expert guidance for your technology decisions'
    }
  ];

  const stats = [
    { number: '100+', label: 'Projects Completed' },
    { number: '50+', label: 'Happy Clients' },
    { number: '10+', label: 'Team Members' },
    { number: '5+', label: 'Years Experience' }
  ];

  const features = [
    'Professional Team',
    '24/7 Support',
    'Latest Technologies',
    'Competitive Pricing',
    'Quality Assurance',
    'On-time Delivery'
  ];

  return (
    <div className="home-page">
      {/* Modern Hero Section */}
      <HeroSection />

      {/* Services Section */}
      <section className="py-5 bg-light">
        <div className="container">
          <div className="row">
            <div className="col-lg-8 mx-auto text-center mb-5">
              <h2 className="display-4 fw-bold text-dark mb-4">Our Services</h2>
              <p className="lead text-muted">
                We offer comprehensive IT solutions to help your business thrive in the digital age.
              </p>
            </div>
          </div>
          <div className="row g-4">
            {services.map((service, index) => (
              <div key={index} className="col-md-6 col-lg-3">
                <div className="card-modern h-100 text-center p-4">
                  <div className="icon-circle icon-circle-primary mx-auto">
                    <i className={`fas fa-${service.icon} fs-3`}></i>
                  </div>
                  <h5 className="fw-bold text-dark mb-3">{service.title}</h5>
                  <p className="text-muted mb-0">{service.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-5 bg-white">
        <div className="container">
          <div className="row">
            {stats.map((stat, index) => (
              <div key={index} className="col-md-6 col-lg-3 text-center mb-4">
                <div className="h1 fw-bold text-gradient mb-2">{stat.number}</div>
                <div className="text-muted">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-5 bg-light">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-6">
              <h2 className="display-4 fw-bold text-dark mb-4">Why Choose <span className="text-gradient">Maruti iT Zone</span>?</h2>
              <p className="lead text-muted mb-4">
                We combine technical expertise with creative thinking to deliver
                exceptional results that drive business growth.
              </p>
              <div className="row">
                {features.map((feature, index) => (
                  <div key={index} className="col-sm-6 mb-3">
                    <div className="d-flex align-items-center">
                      <i className="fas fa-check text-success me-3"></i>
                      <span className="text-dark">{feature}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="col-lg-6">
              <div className="card-modern shadow-modern p-5 text-center">
                <div className="icon-circle icon-circle-primary mx-auto mb-4">
                  <i className="fas fa-users fs-1"></i>
                </div>
                <h3 className="fw-bold text-dark mb-3">Ready to Get Started?</h3>
                <p className="text-muted mb-4">
                  Let's discuss your project and see how we can help bring your vision to life.
                </p>
                <Link
                  to="/contact"
                  className="btn btn-brand-primary btn-lg"
                >
                  Contact Us Today
                  <i className="fas fa-arrow-right ms-2"></i>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-primary py-5 text-white text-center">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-8">
              <h2 className="display-4 fw-bold mb-4">Let's Build Something Amazing Together</h2>
              <p className="lead mb-4 opacity-90">
                Ready to transform your business with cutting-edge technology?
                Get in touch with us today and let's discuss your next project.
              </p>
              <div className="d-flex flex-wrap justify-content-center gap-3">
                <Link
                  to="/contact"
                  className="btn btn-light btn-lg rounded-pill px-4"
                >
                  <i className="fas fa-quote-left me-2"></i>
                  Get Free Quote
                </Link>
                <Link
                  to="/portfolio"
                  className="btn btn-outline-light btn-lg rounded-pill px-4"
                >
                  <i className="fas fa-eye me-2"></i>
                  View Our Work
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
