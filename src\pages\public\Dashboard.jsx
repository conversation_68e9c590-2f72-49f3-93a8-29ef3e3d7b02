
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ge } from 'reactstrap';
import { useAuth } from '../../context/AuthContext';
import { FaUser, FaProjectDiagram, FaCalendarAlt, FaEnvelope, FaPhone, FaCog, FaSignOutAlt, FaEdit } from 'react-icons/fa';

const Dashboard = () => {
  const { user, logout } = useAuth();

  const userProjects = [
    {
      id: 1,
      name: 'E-commerce Website',
      status: 'In Progress',
      progress: 75,
      startDate: '2024-01-15',
      estimatedCompletion: '2024-02-28',
      description: 'Modern e-commerce platform with payment integration'
    },
    {
      id: 2,
      name: 'Mobile App Development',
      status: 'Planning',
      progress: 25,
      startDate: '2024-02-01',
      estimatedCompletion: '2024-04-15',
      description: 'Cross-platform mobile application for inventory management'
    }
  ];

  const recentActivities = [
    {
      id: 1,
      action: 'Project milestone completed',
      project: 'E-commerce Website',
      date: '2024-01-20',
      type: 'success'
    },
    {
      id: 2,
      action: 'New project proposal submitted',
      project: 'Mobile App Development',
      date: '2024-01-18',
      type: 'info'
    },
    {
      id: 3,
      action: 'Meeting scheduled',
      project: 'E-commerce Website',
      date: '2024-01-16',
      type: 'warning'
    }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed': return 'success';
      case 'In Progress': return 'primary';
      case 'Planning': return 'warning';
      case 'On Hold': return 'secondary';
      default: return 'secondary';
    }
  };

  const getActivityColor = (type) => {
    switch (type) {
      case 'success': return 'success';
      case 'info': return 'info';
      case 'warning': return 'warning';
      case 'danger': return 'danger';
      default: return 'secondary';
    }
  };

  return (
    <div className="user-dashboard" style={{ minHeight: '100vh', background: '#F8FAFC', paddingTop: '2rem', paddingBottom: '2rem' }}>
      <Container>
        {/* Header */}
        <Row className="mb-4">
          <Col>
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <h2 className="mb-1" style={{ color: '#1E293B', fontWeight: '700' }}>
                  Welcome back, {user?.name}!
                </h2>
                <p className="text-muted mb-0">Here's an overview of your projects and activities</p>
              </div>
              <div className="d-flex gap-2">
                <Button 
                  color="outline-primary" 
                  size="sm"
                  className="d-flex align-items-center"
                >
                  <FaCog className="me-2" />
                  Settings
                </Button>
                <Button 
                  color="outline-danger" 
                  size="sm"
                  onClick={logout}
                  className="d-flex align-items-center"
                >
                  <FaSignOutAlt className="me-2" />
                  Logout
                </Button>
              </div>
            </div>
          </Col>
        </Row>

        <Row>
          {/* User Profile Card */}
          <Col lg={4} className="mb-4">
            <Card className="shadow-sm border-0" style={{ borderRadius: '1rem' }}>
              <CardHeader className="bg-white border-0 text-center" style={{ borderRadius: '1rem 1rem 0 0' }}>
                <h5 className="mb-0">Profile Information</h5>
              </CardHeader>
              <CardBody className="text-center">
                <div 
                  className="mb-3 mx-auto"
                  style={{
                    width: '100px',
                    height: '100px',
                    background: 'linear-gradient(135deg, #FF5722 0%, #2196F3 100%)',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '2.5rem',
                    fontWeight: 'bold'
                  }}
                >
                  <FaUser />
                </div>
                <h5 className="mb-2">{user?.name}</h5>
                <p className="text-muted mb-3">{user?.email}</p>
                <Badge 
                  color="primary" 
                  className="mb-3"
                  style={{ textTransform: 'capitalize' }}
                >
                  {user?.role} Account
                </Badge>
                
                <div className="text-start">
                  <div className="d-flex align-items-center mb-2">
                    <FaEnvelope className="text-muted me-2" />
                    <small>{user?.email}</small>
                  </div>
                  <div className="d-flex align-items-center mb-2">
                    <FaCalendarAlt className="text-muted me-2" />
                    <small>Member since {user?.createdAt}</small>
                  </div>
                  <div className="d-flex align-items-center mb-3">
                    <FaPhone className="text-muted me-2" />
                    <small>+91 98765 43210</small>
                  </div>
                </div>
                
                <Button 
                  color="outline-primary" 
                  size="sm" 
                  className="w-100"
                >
                  <FaEdit className="me-2" />
                  Edit Profile
                </Button>
              </CardBody>
            </Card>
          </Col>

          {/* Projects Overview */}
          <Col lg={8} className="mb-4">
            <Card className="shadow-sm border-0" style={{ borderRadius: '1rem' }}>
              <CardHeader className="bg-white border-0 d-flex justify-content-between align-items-center" style={{ borderRadius: '1rem 1rem 0 0' }}>
                <h5 className="mb-0">My Projects</h5>
                <Button color="primary" size="sm">
                  <FaProjectDiagram className="me-2" />
                  New Project
                </Button>
              </CardHeader>
              <CardBody>
                {userProjects.map((project) => (
                  <Card key={project.id} className="mb-3 border-0 bg-light">
                    <CardBody>
                      <div className="d-flex justify-content-between align-items-start mb-3">
                        <div>
                          <h6 className="mb-1">{project.name}</h6>
                          <p className="text-muted small mb-2">{project.description}</p>
                        </div>
                        <Badge color={getStatusColor(project.status)}>
                          {project.status}
                        </Badge>
                      </div>
                      
                      <div className="mb-3">
                        <div className="d-flex justify-content-between align-items-center mb-1">
                          <small className="text-muted">Progress</small>
                          <small className="text-muted">{project.progress}%</small>
                        </div>
                        <div className="progress" style={{ height: '6px' }}>
                          <div 
                            className="progress-bar"
                            style={{ 
                              width: `${project.progress}%`,
                              background: 'linear-gradient(135deg, #FF5722 0%, #2196F3 100%)'
                            }}
                          />
                        </div>
                      </div>
                      
                      <Row className="text-center">
                        <Col>
                          <small className="text-muted d-block">Start Date</small>
                          <small className="fw-semibold">{project.startDate}</small>
                        </Col>
                        <Col>
                          <small className="text-muted d-block">Est. Completion</small>
                          <small className="fw-semibold">{project.estimatedCompletion}</small>
                        </Col>
                      </Row>
                    </CardBody>
                  </Card>
                ))}
              </CardBody>
            </Card>
          </Col>
        </Row>

        {/* Recent Activities */}
        <Row>
          <Col>
            <Card className="shadow-sm border-0" style={{ borderRadius: '1rem' }}>
              <CardHeader className="bg-white border-0" style={{ borderRadius: '1rem 1rem 0 0' }}>
                <h5 className="mb-0">Recent Activities</h5>
              </CardHeader>
              <CardBody>
                {recentActivities.map((activity) => (
                  <div key={activity.id} className="d-flex align-items-center mb-3 pb-3 border-bottom">
                    <div 
                      className="me-3"
                      style={{
                        width: '40px',
                        height: '40px',
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '1rem'
                      }}
                    >
                      <Badge color={getActivityColor(activity.type)} className="rounded-circle p-2">
                        <FaProjectDiagram />
                      </Badge>
                    </div>
                    <div className="flex-grow-1">
                      <h6 className="mb-1">{activity.action}</h6>
                      <p className="text-muted small mb-1">Project: {activity.project}</p>
                      <small className="text-muted">{activity.date}</small>
                    </div>
                  </div>
                ))}
              </CardBody>
            </Card>
          </Col>
        </Row>

        {/* Quick Actions */}
        <Row className="mt-4">
          <Col>
            <Card className="shadow-sm border-0" style={{ borderRadius: '1rem' }}>
              <CardHeader className="bg-white border-0" style={{ borderRadius: '1rem 1rem 0 0' }}>
                <h5 className="mb-0">Quick Actions</h5>
              </CardHeader>
              <CardBody>
                <Row>
                  <Col md={3} className="mb-3">
                    <Button 
                      color="outline-primary" 
                      className="w-100 h-100 d-flex flex-column align-items-center justify-content-center"
                      style={{ minHeight: '100px' }}
                    >
                      <FaProjectDiagram className="mb-2" style={{ fontSize: '2rem' }} />
                      <span>Request Quote</span>
                    </Button>
                  </Col>
                  <Col md={3} className="mb-3">
                    <Button 
                      color="outline-success" 
                      className="w-100 h-100 d-flex flex-column align-items-center justify-content-center"
                      style={{ minHeight: '100px' }}
                    >
                      <FaCalendarAlt className="mb-2" style={{ fontSize: '2rem' }} />
                      <span>Schedule Meeting</span>
                    </Button>
                  </Col>
                  <Col md={3} className="mb-3">
                    <Button 
                      color="outline-warning" 
                      className="w-100 h-100 d-flex flex-column align-items-center justify-content-center"
                      style={{ minHeight: '100px' }}
                    >
                      <FaEnvelope className="mb-2" style={{ fontSize: '2rem' }} />
                      <span>Contact Support</span>
                    </Button>
                  </Col>
                  <Col md={3} className="mb-3">
                    <Button 
                      color="outline-info" 
                      className="w-100 h-100 d-flex flex-column align-items-center justify-content-center"
                      style={{ minHeight: '100px' }}
                    >
                      <FaCog className="mb-2" style={{ fontSize: '2rem' }} />
                      <span>Account Settings</span>
                    </Button>
                  </Col>
                </Row>
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default Dashboard;
