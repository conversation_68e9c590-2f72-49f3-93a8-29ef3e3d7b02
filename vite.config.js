import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    host: '0.0.0.0',
    port: 5173, // optional — local dev only
  },
  base: '/', // if deploying at root — change to '/subfolder/' if needed
  build: {
    outDir: 'dist', // default — can customize if you want
    sourcemap: false, // disable sourcemaps for smaller production bundle
    minify: 'esbuild', // use esbuild for fast minification
    target: 'esnext', // modern output — fallback to 'es2015' if needed
    chunkSizeWarningLimit: 1000, // optional
  }
})
