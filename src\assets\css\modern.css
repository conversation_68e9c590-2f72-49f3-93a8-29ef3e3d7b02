/* Modern Maruti IT Zone Design */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* Company Brand Colors */
:root {
  /* Primary Brand Colors from Logo */
  --brand-orange: #FF5722;
  --brand-blue: #2196F3;
  --brand-dark-blue: #1976D2;
  --brand-light-orange: #FF7043;
  
  /* Gradient Colors */
  --gradient-primary: linear-gradient(135deg, #FF5722 0%, #2196F3 100%);
  --gradient-secondary: linear-gradient(135deg, #FF7043 0%, #1976D2 100%);
  --gradient-hero: linear-gradient(135deg, #FF5722 0%, #FF7043 25%, #2196F3 75%, #1976D2 100%);
  
  /* Neutral Colors */
  --white: #FFFFFF;
  --light-gray: #F5F5F5;
  --gray: #9E9E9E;
  --dark-gray: #424242;
  --black: #212121;
  
  /* Text Colors */
  --text-primary: #212121;
  --text-secondary: #757575;
  --text-light: #FFFFFF;
  
  /* Background Colors */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F5F5F5;
  --bg-dark: #212121;
  
  /* Shadow */
  --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
  --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
  --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
  --shadow-xl: 0 20px 25px rgba(0,0,0,0.15);
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
}

.display-1 { font-size: 6rem; font-weight: 900; }
.display-2 { font-size: 5.5rem; font-weight: 800; }
.display-3 { font-size: 4.5rem; font-weight: 800; }
.display-4 { font-size: 3.5rem; font-weight: 700; }

/* Brand Text */
.brand-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

/* Buttons */
.btn-brand-primary {
  background: var(--gradient-primary);
  border: none;
  color: white;
  font-weight: 600;
  padding: 12px 32px;
  border-radius: 50px;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-md);
}

.btn-brand-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  color: white;
}

.btn-brand-outline {
  background: transparent;
  border: 2px solid var(--brand-orange);
  color: var(--brand-orange);
  font-weight: 600;
  padding: 10px 30px;
  border-radius: 50px;
  transition: all 0.3s ease;
}

.btn-brand-outline:hover {
  background: var(--brand-orange);
  color: white;
  transform: translateY(-2px);
}

/* Header Styles */
.navbar-brand {
  font-size: 1.5rem;
  font-weight: 800;
}

.navbar-nav .nav-link {
  font-weight: 500;
  color: var(--text-primary) !important;
  padding: 0.75rem 1rem !important;
  transition: all 0.3s ease;
  position: relative;
}

.navbar-nav .nav-link:hover {
  color: var(--brand-orange) !important;
}

.navbar-nav .nav-link.active {
  color: var(--brand-orange) !important;
  font-weight: 600;
}

.navbar-nav .nav-link.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background: var(--gradient-primary);
  border-radius: 2px;
}

/* Mobile Menu */
.navbar-toggler {
  border: none;
  padding: 0.25rem 0.5rem;
}

.navbar-toggler:focus {
  box-shadow: none;
}

.navbar-collapse {
  background: white;
  border-radius: 10px;
  box-shadow: var(--shadow-lg);
  margin-top: 1rem;
  padding: 1rem;
}

@media (max-width: 991.98px) {
  .navbar-collapse {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
  }
  
  .navbar-nav .nav-link {
    padding: 1rem !important;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .navbar-nav .nav-link:last-child {
    border-bottom: none;
  }
}

/* Hero Section */
.hero-gradient {
  background: var(--gradient-hero);
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.hero-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
  opacity: 0.3;
}

.hero-content {
  position: relative;
  z-index: 2;
}

/* Cards */
.card-modern {
  border: none;
  border-radius: 20px;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  overflow: hidden;
}

.card-modern:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-xl);
}

.card-gradient {
  background: var(--gradient-primary);
  color: white;
}

/* Breadcrumb */
.breadcrumb-modern {
  background: var(--bg-secondary);
  padding: 1rem 0;
  margin: 0;
}

.breadcrumb-modern .breadcrumb {
  background: transparent;
  margin: 0;
  padding: 0;
}

.breadcrumb-modern .breadcrumb-item a {
  color: var(--brand-orange);
  text-decoration: none;
  font-weight: 500;
}

.breadcrumb-modern .breadcrumb-item.active {
  color: var(--text-primary);
  font-weight: 600;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Responsive */
@media (max-width: 768px) {
  .display-3 { font-size: 2.5rem; }
  .display-4 { font-size: 2rem; }
  
  .hero-gradient {
    min-height: 80vh;
  }
  
  .btn-brand-primary,
  .btn-brand-outline {
    padding: 10px 24px;
    font-size: 0.9rem;
  }
}

@media (max-width: 576px) {
  .display-3 { font-size: 2rem; }
  .display-4 { font-size: 1.75rem; }
  
  .hero-gradient {
    min-height: 70vh;
  }
}

/* Utility Classes */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bg-gradient-primary {
  background: var(--gradient-primary);
}

.bg-gradient-secondary {
  background: var(--gradient-secondary);
}

.shadow-modern {
  box-shadow: var(--shadow-lg);
}

/* Footer */
.footer-modern {
  background: var(--bg-dark);
  color: var(--text-light);
  padding: 3rem 0 1rem;
}

.footer-modern a {
  color: var(--text-light);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-modern a:hover {
  color: var(--brand-orange);
}

/* Icon Styles */
.icon-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.icon-circle-primary {
  background: linear-gradient(135deg, rgba(255, 87, 34, 0.1) 0%, rgba(33, 150, 243, 0.1) 100%);
  color: var(--brand-orange);
}

/* Page Content Wrapper */
.page-content-wrapper {
  padding-top: 75px;
}

@media (max-width: 991.98px) {
  .page-content-wrapper {
    padding-top: 60px;
  }
}

/* Modern Header Styles */
.top-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1051;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  transition: all 0.3s ease;
}

.top-bar a:hover {
  color: var(--brand-orange) !important;
  transition: color 0.3s ease;
}

.hover-scale {
  transition: transform 0.2s ease;
}

.hover-scale:hover {
  transform: scale(1.1);
}

.main-navbar {
  top: 28px;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(15px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: 1px solid rgba(0,0,0,0.05);
  padding: 0.25rem 0;
}

.main-navbar.navbar-scrolled {
  background: rgba(255, 255, 255, 0.98) !important;
  border-bottom: 1px solid rgba(0,0,0,0.1);
  box-shadow: 0 2px 20px rgba(0,0,0,0.1);
}

@media (max-width: 991.98px) {
  .main-navbar {
    top: 0;
    padding: 0.5rem 0;
  }
}

/* Brand Info Styling */
.brand-info {
  line-height: 1.1;
}

.brand-name {
  margin-bottom: -3px;
}

.brand-tagline {
  line-height: 1;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
  background: rgba(0,0,0,0.05);
}

.hamburger {
  width: 24px;
  height: 18px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.hamburger span {
  display: block;
  width: 100%;
  height: 2px;
  background: #333;
  border-radius: 1px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hamburger.active span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger.active span:nth-child(2) {
  opacity: 0;
}

.hamburger.active span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Backdrop */
.mobile-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.6);
  z-index: 1055;
  backdrop-filter: blur(2px);
}

/* Mobile Sidebar */
.mobile-sidebar {
  position: fixed;
  top: 0;
  left: -350px;
  width: 320px;
  max-width: 85vw;
  height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  z-index: 1060;
  transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 2px 0 30px rgba(0,0,0,0.2);
  border-right: 3px solid var(--brand-orange);
}

.mobile-sidebar.active {
  left: 0;
}

.mobile-sidebar-content {
  height: 100%;
  overflow-y: auto;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
}

/* Mobile Header */
.mobile-header {
  display: flex;
  justify-content: between;
  align-items: center;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(0,0,0,0.1);
  margin-bottom: 1.5rem;
}

.mobile-close-btn {
  background: none;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: all 0.3s ease;
}

.mobile-close-btn:hover {
  background: rgba(0,0,0,0.1);
  color: #333;
}

/* Mobile Navigation */
.mobile-nav {
  flex: 1;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  padding: 0.875rem 1rem;
  margin: 0.25rem 0;
  border-radius: 12px;
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.mobile-nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  transition: left 0.3s ease;
  z-index: -1;
}

.mobile-nav-link:hover::before,
.mobile-nav-link.active::before {
  left: 0;
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
  color: white;
  transform: translateX(8px);
  box-shadow: 0 4px 12px rgba(255, 87, 34, 0.3);
}

.mobile-nav-link i {
  width: 20px;
  margin-right: 12px;
  font-size: 1rem;
}

/* Mobile Services Submenu */
.mobile-nav-group {
  margin: 0.25rem 0;
}

.mobile-nav-label {
  display: flex;
  align-items: center;
  padding: 0.875rem 1rem;
  font-weight: 600;
  color: #555;
  border-radius: 12px;
  background: rgba(0,0,0,0.03);
}

.mobile-nav-label i {
  width: 20px;
  margin-right: 12px;
}

.mobile-submenu {
  margin-left: 2rem;
  margin-top: 0.5rem;
}

.mobile-submenu-link {
  display: block;
  padding: 0.5rem 1rem;
  margin: 0.25rem 0;
  border-radius: 8px;
  text-decoration: none;
  color: #666;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.mobile-submenu-link:hover {
  background: var(--gradient-primary);
  color: white;
  transform: translateX(5px);
}

/* Mobile Actions */
.mobile-actions {
  padding: 1.5rem 0;
  border-top: 1px solid rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.mobile-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.mobile-btn-brand-outline {
  border: 2px solid var(--brand-orange);
  color: var(--brand-orange);
  background: transparent;
}

.mobile-btn-brand-outline:hover {
  background: var(--brand-orange);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 87, 34, 0.3);
}

.mobile-btn-brand-primary {
  background: var(--gradient-primary);
  color: white;
  border: 2px solid transparent;
}

.mobile-btn-brand-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 87, 34, 0.4);
}

/* Mobile Contact */
.mobile-contact {
  padding-top: 1rem;
  border-top: 1px solid rgba(0,0,0,0.1);
}

.mobile-contact-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  font-size: 0.9rem;
  color: #666;
}

.mobile-contact-item i {
  width: 20px;
  margin-right: 12px;
}

/* Loading Animation */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 87, 34, 0.1);
  border-left: 4px solid var(--brand-orange);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Modern Footer Styles */
.footer-modern-new {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  position: relative;
  overflow: hidden;
}

.footer-modern-new::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(255, 87, 34, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(33, 150, 243, 0.1) 0%, transparent 50%);
  z-index: 1;
}

.footer-main {
  position: relative;
  z-index: 2;
  padding: 4rem 0 2rem;
  color: white;
}

/* Brand Section */
.footer-brand .brand-logo-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.footer-logo {
  width: 50px;
  height: 50px;
  object-fit: contain;
  filter: brightness(0) invert(1);
}

.brand-text-section .brand-name {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #FF5722 0%, #2196F3 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-text-section .brand-tagline {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  font-style: italic;
}

.company-description {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 2rem;
}

/* Social Links */
.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.social-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  transition: all 0.3s ease;
  z-index: -1;
}

.social-link.facebook::before { background: #1877f2; }
.social-link.twitter::before { background: #1da1f2; }
.social-link.linkedin::before { background: #0077b5; }
.social-link.instagram::before { background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%); }
.social-link.youtube::before { background: #ff0000; }

.social-link {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.social-link:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  color: white;
}

.social-link:hover::before {
  opacity: 1;
}

/* Footer Sections */
.footer-section .footer-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: white;
  position: relative;
  padding-bottom: 0.5rem;
}

.footer-section .footer-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background: linear-gradient(135deg, #FF5722 0%, #2196F3 100%);
  border-radius: 1px;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.75rem;
}

.footer-link {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  padding: 0.25rem 0;
}

.footer-link:hover {
  color: #FF5722;
  transform: translateX(5px);
}

.footer-link i {
  width: 16px;
  color: #FF5722;
}

/* Contact Info */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.contact-icon {
  width: 20px;
  height: 20px;
  color: #FF5722;
  margin-top: 0.25rem;
  flex-shrink: 0;
}

.contact-details p,
.contact-details a {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  line-height: 1.4;
}

.contact-details a:hover {
  color: #FF5722;
}

/* Footer Bottom */
.footer-bottom {
  background: rgba(0, 0, 0, 0.3);
  padding: 1.5rem 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 2;
}

.copyright {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.footer-bottom-links {
  display: flex;
  justify-content: flex-end;
  gap: 2rem;
}

.footer-bottom-link {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-bottom-link:hover {
  color: #FF5722;
}

/* Back to Top Button */
.back-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #FF5722 0%, #2196F3 100%);
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: 0 4px 15px rgba(255, 87, 34, 0.3);
}

.back-to-top:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255, 87, 34, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .footer-main {
    padding: 3rem 0 1.5rem;
  }

  .footer-brand .brand-logo-section {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .social-links {
    justify-content: center;
  }

  .footer-bottom-links {
    justify-content: center;
    margin-top: 1rem;
  }

  .back-to-top {
    bottom: 1rem;
    right: 1rem;
    width: 45px;
    height: 45px;
  }
}

/* Hero Section Floating Elements */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-item {
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  animation: float 6s ease-in-out infinite;
}

.floating-item-1 { top: 10%; left: 5%; animation-delay: 0s; color: #61DAFB; }
.floating-item-2 { top: 20%; right: 10%; animation-delay: 1s; color: #F7DF1E; }
.floating-item-3 { top: 35%; left: 8%; animation-delay: 2s; color: #3776AB; }
.floating-item-4 { top: 50%; right: 5%; animation-delay: 3s; color: #339933; }
.floating-item-5 { top: 65%; left: 12%; animation-delay: 4s; color: #E34F26; }
.floating-item-6 { top: 75%; right: 15%; animation-delay: 5s; color: #1572B6; }
.floating-item-7 { top: 15%; left: 25%; animation-delay: 1.5s; color: #DD0031; }
.floating-item-8 { top: 45%; left: 20%; animation-delay: 2.5s; color: #4FC08D; }
.floating-item-9 { top: 80%; left: 30%; animation-delay: 3.5s; color: #777BB4; }
.floating-item-10 { top: 25%; right: 25%; animation-delay: 4.5s; color: #FF2D20; }
.floating-item-11 { top: 60%; right: 20%; animation-delay: 0.5s; color: #2496ED; }
.floating-item-12 { top: 85%; right: 30%; animation-delay: 1.8s; color: #FF9900; }

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-20px) rotate(5deg); }
  50% { transform: translateY(-10px) rotate(-5deg); }
  75% { transform: translateY(-15px) rotate(3deg); }
}

/* Programmer Showcase */
.programmer-showcase {
  position: relative;
  height: 500px;
  width: 100%;
}

.programmer-card {
  position: absolute;
  width: 280px;
  height: 180px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: cardFloat 4s ease-in-out infinite;
}

.programmer-1 {
  top: 0;
  right: 0;
  animation-delay: 0s;
}

.programmer-2 {
  top: 50%;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 1.3s;
}

.programmer-3 {
  bottom: 0;
  right: 0;
  animation-delay: 2.6s;
}

.programmer-shadow {
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 20px;
  border-radius: 50%;
  filter: blur(10px);
  animation: shadowPulse 4s ease-in-out infinite;
}

.shadow-1 {
  background: linear-gradient(90deg, rgba(255, 87, 34, 0.3), rgba(33, 150, 243, 0.3));
  animation-delay: 0s;
}

.shadow-2 {
  background: linear-gradient(90deg, rgba(76, 175, 80, 0.3), rgba(255, 193, 7, 0.3));
  animation-delay: 1.3s;
}

.shadow-3 {
  background: linear-gradient(90deg, rgba(156, 39, 176, 0.3), rgba(233, 30, 99, 0.3));
  animation-delay: 2.6s;
}

.programmer-image {
  display: flex;
  align-items: center;
  gap: 1rem;
  height: 100%;
}

.programmer-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #FF5722 0%, #2196F3 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
}

.programmer-info {
  flex: 1;
}

.programmer-info h6 {
  color: white;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.programmer-info p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.85rem;
  margin-bottom: 0.75rem;
}

.skill-badges {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.skill-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
}

.programmer-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.programmer-card:hover .programmer-shadow {
  transform: translateX(-50%) scale(1.1);
  opacity: 0.8;
}

@keyframes cardFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
}

@keyframes shadowPulse {
  0%, 100% { opacity: 0.3; transform: translateX(-50%) scale(1); }
  50% { opacity: 0.6; transform: translateX(-50%) scale(1.1); }
}

/* Remove Default Link Styles and Focus Outlines */
a, a:hover, a:focus, a:active, a:visited {
  text-decoration: none !important;
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
}

a:focus, a:active {
  outline: 0 !important;
  box-shadow: none !important;
  border: none !important;
}

/* Remove blue highlight on touch devices */
a, button, input, textarea, select {
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

/* Remove focus outline from all interactive elements */
*:focus, *:active {
  outline: none !important;
  box-shadow: none !important;
}

/* Custom focus styles for accessibility (optional) */
.btn:focus, .nav-link:focus {
  box-shadow: 0 0 0 2px rgba(255, 87, 34, 0.3) !important;
  outline: none !important;
}

/* Remove default button focus styles */
button:focus, button:active {
  outline: none !important;
  box-shadow: none !important;
}

/* Fix specific link hover issues */
.navbar-nav .nav-link:hover,
.navbar-nav .nav-link:focus,
.navbar-nav .nav-link:active {
  background: none !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

/* Footer links */
.footer-link:hover,
.footer-link:focus,
.footer-link:active {
  background: none !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

/* Mobile navigation links */
.mobile-nav-link:hover,
.mobile-nav-link:focus,
.mobile-nav-link:active {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

/* Social links */
.social-link:hover,
.social-link:focus,
.social-link:active {
  border: none !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
  outline: none !important;
}

/* Remove Bootstrap default focus styles */
.btn:not(:disabled):not(.disabled):active:focus,
.btn:not(:disabled):not(.disabled).active:focus {
  box-shadow: none !important;
}

/* Remove any remaining blue outlines */
input:focus,
textarea:focus,
select:focus {
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(255, 87, 34, 0.3) !important;
  border-color: var(--brand-orange) !important;
}

/* Responsive adjustments */
@media (max-width: 991.98px) {
  .floating-item {
    width: 45px;
    height: 45px;
    font-size: 1.2rem;
  }

  .programmer-showcase {
    height: 400px;
  }

  .programmer-card {
    width: 240px;
    height: 150px;
    padding: 1rem;
  }

  .programmer-avatar {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }
}

@media (max-width: 767.98px) {
  .floating-item {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .programmer-showcase {
    height: 300px;
    margin-top: 2rem;
  }

  .programmer-card {
    width: 200px;
    height: 120px;
    padding: 0.75rem;
  }

  .programmer-1 {
    top: 0;
    left: 0;
  }

  .programmer-2 {
    top: 40%;
    right: 0;
    left: auto;
    transform: none;
  }

  .programmer-3 {
    bottom: 0;
    left: 50%;
    right: auto;
    transform: translateX(-50%);
  }

  .programmer-avatar {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .programmer-info h6 {
    font-size: 0.9rem;
  }

  .programmer-info p {
    font-size: 0.75rem;
  }

  .skill-badge {
    font-size: 0.6rem;
    padding: 0.2rem 0.4rem;
  }
}
