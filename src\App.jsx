import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import { Suspense, lazy } from 'react';
import 'bootstrap/dist/css/bootstrap.min.css';
import './assets/css/modern.css';
import './assets/css/index.css';

// Layouts (not lazy loaded for better UX)
import PublicLayout from './layouts/public/PublicLayout';
import AuthorizedLayout from './layouts/authorized/AuthorizedLayout';

// Context and Components
import { AuthProvider } from './context/AuthContext';
import ProtectedRoute from './components/common/ProtectedRoute';

// Loading Component
const LoadingSpinner = () => (
  <div className="min-vh-100 d-flex align-items-center justify-content-center">
    <div className="text-center">
      <div className="spinner-border text-orange" role="status" style={{ width: '3rem', height: '3rem' }}>
        <span className="visually-hidden">Loading...</span>
      </div>
      <p className="mt-3 text-muted">Loading page...</p>
    </div>
  </div>
);

// Lazy Load Public Pages
const Home = lazy(() => import('./pages/public/Home'));
const About = lazy(() => import('./pages/public/About'));
const Services = lazy(() => import('./pages/public/Services'));
const Portfolio = lazy(() => import('./pages/public/Portfolio'));
const Team = lazy(() => import('./pages/public/Team'));
const Contact = lazy(() => import('./pages/public/Contact'));
const Careers = lazy(() => import('./pages/public/Careers'));
const Login = lazy(() => import('./pages/public/Login'));
const Register = lazy(() => import('./pages/public/Register'));
const UserDashboard = lazy(() => import('./pages/public/Dashboard'));
const PrivacyPolicy = lazy(() => import('./pages/public/PrivacyPolicy'));
const TermsOfService = lazy(() => import('./pages/public/TermsOfService'));
const Sitemap = lazy(() => import('./pages/public/Sitemap'));

// Lazy Load Authorized/Admin Pages
const AdminDashboard = lazy(() => import('./pages/authorized/Dashboard'));
const ManageServices = lazy(() => import('./pages/authorized/ManageServices'));
const ManagePortfolio = lazy(() => import('./pages/authorized/ManagePortfolio'));
const ManageTeam = lazy(() => import('./pages/authorized/ManageTeam'));
const Settings = lazy(() => import('./pages/authorized/Settings'));

function App() {
  return (
    <HelmetProvider>
      <AuthProvider>
        <Router>
          <PublicLayout>
            <Suspense fallback={<LoadingSpinner />}>
              <Routes>
                {/* Public Routes */}
                <Route path="/" element={<Home />} />
                <Route path="/about" element={<About />} />
                <Route path="/about-us" element={<About />} />
                <Route path="/services" element={<Services />} />
                <Route path="/services/:serviceType" element={<Services />} />
                <Route path="/portfolio" element={<Portfolio />} />
                <Route path="/team" element={<Team />} />
                <Route path="/careers" element={<Careers />} />
                <Route path="/contact" element={<Contact />} />
                <Route path="/contact-us" element={<Contact />} />
                <Route path="/login" element={<Login />} />
                <Route path="/register" element={<Register />} />
                <Route path="/privacy-policy" element={<PrivacyPolicy />} />
                <Route path="/terms-of-service" element={<TermsOfService />} />
                <Route path="/sitemap" element={<Sitemap />} />

              {/* Protected User Dashboard */}
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <UserDashboard />
                  </ProtectedRoute>
                }
              />

              {/* Authorized/Admin Routes */}
              <Route 
                path="/authorized" 
                element={
                  <ProtectedRoute requireAdmin={true}>
                    <AuthorizedLayout />
                  </ProtectedRoute>
                }
              >
                <Route index element={<AdminDashboard />} />
                <Route path="services" element={<ManageServices />} />
                <Route path="portfolio" element={<ManagePortfolio />} />
                <Route path="team" element={<ManageTeam />} />
                <Route path="settings" element={<Settings />} />
              </Route>

              {/* 404 Route */}
              <Route path="*" element={
                <div className="min-vh-100 d-flex align-items-center justify-content-center bg-light">
                  <div className="text-center">
                    <h1 className="display-1 fw-bold text-primary">404</h1>
                    <h2 className="mb-4">Page Not Found</h2>
                    <p className="text-muted mb-4">The page you're looking for doesn't exist.</p>
                    <a href="/" className="btn btn-primary btn-lg">
                      Go Back Home
                    </a>
                  </div>
                </div>
              } />
              </Routes>
            </Suspense>
          </PublicLayout>
        </Router>
      </AuthProvider>
    </HelmetProvider>
  );
}

export default App;
