
import {
  FaReact,
  FaNodeJs,
  FaPython,
  FaPhp,
  FaLaravel,
  FaAngular,
  FaVuejs,
  FaJava,
  FaHtml5,
  FaCss3Alt,
  FaBootstrap,
  FaGitAlt,
  FaDocker,
  FaAws
} from 'react-icons/fa';
import {
  SiMongodb,
  SiExpress,
  SiMysql,
  SiJavascript,
  SiTypescript,
  SiNextdotjs,
  SiDjango,
  SiFlask,
  SiPostgresql,
  SiRedis,
  SiFirebase,
  SiGraphql,
  SiTailwindcss,
  SiKubernetes,
  SiGooglecloud,
  SiWordpress,
  SiShopify,
  SiFlutter,
  SiMicrosoftazure
} from 'react-icons/si';

const FloatingTechIcons = () => {
  const techIcons = [
    // Frontend Technologies
    { icon: FaReact, color: '#61DAFB', name: 'React' },
    { icon: FaAngular, color: '#DD0031', name: 'Angular' },
    { icon: FaVuejs, color: '#4FC08D', name: 'Vue.js' },
    { icon: SiNextdotjs, color: '#000000', name: 'Next.js' },
    { icon: FaHtml5, color: '#E34F26', name: 'HTML5' },
    { icon: FaCss3Alt, color: '#1572B6', name: 'CSS3' },
    { icon: SiJavascript, color: '#F7DF1E', name: 'JavaScript' },
    { icon: SiTypescript, color: '#3178C6', name: 'TypeScript' },
    { icon: FaBootstrap, color: '#7952B3', name: 'Bootstrap' },
    { icon: SiTailwindcss, color: '#06B6D4', name: 'Tailwind CSS' },

    // Backend Technologies
    { icon: FaNodeJs, color: '#339933', name: 'Node.js' },
    { icon: FaPython, color: '#3776AB', name: 'Python' },
    { icon: FaPhp, color: '#777BB4', name: 'PHP' },
    { icon: FaLaravel, color: '#FF2D20', name: 'Laravel' },
    { icon: SiDjango, color: '#092E20', name: 'Django' },
    { icon: SiFlask, color: '#000000', name: 'Flask' },
    { icon: SiExpress, color: '#000000', name: 'Express.js' },
    { icon: FaJava, color: '#ED8B00', name: 'Java' },

    // Databases
    { icon: SiMongodb, color: '#47A248', name: 'MongoDB' },
    { icon: SiMysql, color: '#4479A1', name: 'MySQL' },
    { icon: SiPostgresql, color: '#336791', name: 'PostgreSQL' },
    { icon: SiRedis, color: '#DC382D', name: 'Redis' },
    { icon: SiFirebase, color: '#FFCA28', name: 'Firebase' },

    // Mobile Development
    { icon: SiFlutter, color: '#02569B', name: 'Flutter' },

    // Cloud & DevOps
    { icon: FaAws, color: '#FF9900', name: 'AWS' },
    { icon: SiGooglecloud, color: '#4285F4', name: 'Google Cloud' },
    { icon: SiMicrosoftazure, color: '#0078D4', name: 'Azure' },
    { icon: FaDocker, color: '#2496ED', name: 'Docker' },
    { icon: SiKubernetes, color: '#326CE5', name: 'Kubernetes' },
    { icon: FaGitAlt, color: '#F05032', name: 'Git' },

    // CMS & E-commerce
    { icon: SiWordpress, color: '#21759B', name: 'WordPress' },
    { icon: SiShopify, color: '#7AB55C', name: 'Shopify' },

    // Other
    { icon: SiGraphql, color: '#E10098', name: 'GraphQL' }
  ];

  // Select a subset of icons for better performance and visual appeal
  const selectedIcons = techIcons.slice(0, 16);

  // Generate better distributed positions
  const generatePositions = (count) => {
    const positions = [];
    const margin = 8; // Margin from edges

    for (let i = 0; i < count; i++) {
      const angle = (i / count) * 2 * Math.PI;
      const radius = 35 + (i % 3) * 10; // Varying radius for depth

      // Convert polar to cartesian coordinates
      const x = 50 + radius * Math.cos(angle);
      const y = 50 + radius * Math.sin(angle);

      // Ensure positions are within bounds
      const clampedX = Math.max(margin, Math.min(100 - margin, x));
      const clampedY = Math.max(margin, Math.min(100 - margin, y));

      positions.push({
        left: `${clampedX}%`,
        top: `${clampedY}%`
      });
    }

    return positions;
  };

  const positions = generatePositions(selectedIcons.length);

  return (
    <div className="floating-icons">
      {selectedIcons.map((tech, index) => {
        const IconComponent = tech.icon;
        const position = positions[index];

        return (
          <div
            key={`${tech.name}-${index}`}
            className="floating-icon"
            title={tech.name}
            style={{
              color: tech.color,
              animationDelay: `${index * 0.4}s`,
              animationDuration: `${4 + (index % 4)}s`,
              ...position
            }}
          >
            <IconComponent />
          </div>
        );
      })}
    </div>
  );
};

export default FloatingTechIcons;
