import { useLocation } from 'react-router-dom';
import Header from '../../components/public/Header';
import Footer from '../../components/public/Footer';
import PageTitle from '../../components/common/PageTitle';

const PublicLayout = ({ children }) => {
  const location = useLocation();
  const isHomePage = location.pathname === '/';

  return (
    <div className="min-vh-100 d-flex flex-column">
      <PageTitle />
      <Header />

      {/* Add padding top to account for fixed header and top bar */}
      <div className={isHomePage ? '' : 'page-content-wrapper'}>
        {/* Main content */}
        <main className="flex-grow-1">
          {children}
        </main>
      </div>

      <Footer />
    </div>
  );
};

export default PublicLayout;
