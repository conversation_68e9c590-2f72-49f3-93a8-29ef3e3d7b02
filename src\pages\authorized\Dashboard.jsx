
import { Container, Row, Col, Card, CardBody, CardHeader, Progress, Badge, Button } from 'reactstrap';
import { FaProjectDiagram, FaUsers, FaDollarSign, FaUserTie, FaChartLine, FaCalendarAlt, FaClock, FaArrowUp, FaArrowDown, FaEye, FaEdit, FaTrash } from 'react-icons/fa';

const Dashboard = () => {

  const stats = [
    {
      title: 'Total Projects',
      value: '150+',
      icon: FaProjectDiagram,
      color: 'var(--primary-orange)',
      bgColor: 'rgba(255, 87, 34, 0.1)',
      change: '+12%',
      trend: 'up',
      description: 'Projects completed this year'
    },
    {
      title: 'Active Clients',
      value: '50+',
      icon: FaUsers,
      color: 'var(--primary-blue)',
      bgColor: 'rgba(33, 150, 243, 0.1)',
      change: '+8%',
      trend: 'up',
      description: 'Currently active clients'
    },
    {
      title: 'Revenue',
      value: '$250K',
      icon: FaDollarSign,
      color: 'var(--success)',
      bgColor: 'rgba(34, 197, 94, 0.1)',
      change: '+15%',
      trend: 'up',
      description: 'Total revenue this year'
    },
    {
      title: 'Team Members',
      value: '15',
      icon: FaUserTie,
      color: 'var(--purple)',
      bgColor: 'rgba(139, 92, 246, 0.1)',
      change: '+2',
      trend: 'up',
      description: 'Active team members'
    }
  ];

  const recentProjects = [
    {
      id: 1,
      name: 'E-commerce Platform',
      client: 'RetailCorp',
      status: 'In Progress',
      progress: 75,
      deadline: '2024-02-15',
      priority: 'High',
      team: 5,
      budget: '$45K'
    },
    {
      id: 2,
      name: 'Mobile Banking App',
      client: 'FinanceBank',
      status: 'Testing',
      progress: 90,
      deadline: '2024-01-30',
      priority: 'Critical',
      team: 8,
      budget: '$120K'
    },
    {
      id: 3,
      name: 'Healthcare Portal',
      client: 'MediCare',
      status: 'Completed',
      progress: 100,
      deadline: '2024-01-20',
      priority: 'Medium',
      team: 3,
      budget: '$25K'
    },
    {
      id: 4,
      name: 'Inventory System',
      client: 'LogisticsPro',
      status: 'Planning',
      progress: 25,
      deadline: '2024-03-10',
      priority: 'Low',
      team: 4,
      budget: '$60K'
    }
  ];

  const quickActions = [
    { title: 'New Project', icon: FaProjectDiagram, color: 'var(--primary-orange)' },
    { title: 'Add Client', icon: FaUsers, color: 'var(--primary-blue)' },
    { title: 'View Reports', icon: FaChartLine, color: 'var(--success)' },
    { title: 'Team Meeting', icon: FaCalendarAlt, color: 'var(--purple)' }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed': return 'var(--success)';
      case 'In Progress': return 'var(--primary-orange)';
      case 'Testing': return 'var(--primary-blue)';
      case 'Planning': return 'var(--text-muted)';
      default: return 'var(--text-muted)';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'Critical': return 'var(--danger)';
      case 'High': return 'var(--warning)';
      case 'Medium': return 'var(--info)';
      case 'Low': return 'var(--success)';
      default: return 'var(--text-muted)';
    }
  };

  return (
    <div className="dashboard-page" style={{
      background: 'var(--secondary)',
      minHeight: '100vh',
      padding: '2rem 0'
    }}>
      <Container fluid className="px-4">
        {/* Welcome Section */}
        <Row className="mb-4">
          <Col>
            <div className="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
              <div className="mb-3 mb-md-0">
                <h1 className="mb-2 fw-bold" style={{
                  color: 'var(--default)',
                  fontSize: '2.5rem'
                }}>
                  Welcome back, Admin! 👋
                </h1>
                <p className="text-muted mb-0 fs-5">
                  Here's what's happening with your business today.
                </p>
              </div>
              <div className="text-end">
                <div className="d-flex flex-column align-items-end">
                  <small className="text-muted mb-1">
                    <FaClock className="me-1" />
                    Last updated: {new Date().toLocaleDateString()}
                  </small>
                  <Badge color="success" className="px-3 py-2">
                    All Systems Operational
                  </Badge>
                </div>
              </div>
            </div>
          </Col>
        </Row>

        {/* Stats Cards */}
        <Row className="mb-5">
          {stats.map((stat, index) => {
            const IconComponent = stat.icon;
            return (
              <Col xl={3} lg={6} md={6} className="mb-4" key={index}>
                <Card className="card-custom h-100 border-0 shadow-sm" style={{
                  background: 'white',
                  transition: 'all 0.3s ease'
                }}>
                  <CardBody className="p-4">
                    <div className="d-flex justify-content-between align-items-start">
                      <div className="flex-grow-1">
                        <div className="d-flex align-items-center mb-3">
                          <div
                            className="rounded-circle d-flex align-items-center justify-content-center me-3"
                            style={{
                              width: '50px',
                              height: '50px',
                              backgroundColor: stat.bgColor,
                              color: stat.color
                            }}
                          >
                            <IconComponent size={24} />
                          </div>
                          <div>
                            <h6 className="text-muted mb-1 text-uppercase" style={{ fontSize: '0.75rem', letterSpacing: '1px' }}>
                              {stat.title}
                            </h6>
                            <h2 className="mb-0 fw-bold" style={{ color: stat.color }}>
                              {stat.value}
                            </h2>
                          </div>
                        </div>
                        <div className="d-flex align-items-center">
                          <div className="d-flex align-items-center me-3">
                            {stat.trend === 'up' ? (
                              <FaArrowUp className="text-success me-1" size={12} />
                            ) : (
                              <FaArrowDown className="text-danger me-1" size={12} />
                            )}
                            <small className={stat.trend === 'up' ? 'text-success' : 'text-danger'} style={{ fontWeight: '600' }}>
                              {stat.change}
                            </small>
                          </div>
                          <small className="text-muted">from last month</small>
                        </div>
                        <small className="text-muted mt-2 d-block" style={{ fontSize: '0.7rem' }}>
                          {stat.description}
                        </small>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </Col>
            );
          })}
        </Row>

        {/* Quick Actions */}
        <Row className="mb-5">
          <Col>
            <h3 className="mb-4 fw-bold" style={{ color: 'var(--default)' }}>
              Quick Actions
            </h3>
            <Row>
              {quickActions.map((action, index) => {
                const IconComponent = action.icon;
                return (
                  <Col xl={3} lg={6} md={6} className="mb-3" key={index}>
                    <Card
                      className="card-custom border-0 shadow-sm h-100"
                      style={{
                        background: 'white',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-5px)';
                        e.currentTarget.style.boxShadow = '0 10px 25px rgba(0,0,0,0.15)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
                      }}
                    >
                      <CardBody className="text-center p-4">
                        <div
                          className="rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center"
                          style={{
                            width: '60px',
                            height: '60px',
                            backgroundColor: `${action.color}20`,
                            color: action.color
                          }}
                        >
                          <IconComponent size={28} />
                        </div>
                        <h6 className="mb-0 fw-bold text-dark">
                          {action.title}
                        </h6>
                      </CardBody>
                    </Card>
                  </Col>
                );
              })}
            </Row>
          </Col>
        </Row>

        {/* Recent Projects */}
        <Row>
          <Col>
            <Card className="card-custom border-0 shadow-sm bg-white">
              <CardHeader
                className="border-0 d-flex justify-content-between align-items-center bg-white"
                style={{
                  padding: '1.5rem'
                }}
              >
                <h4 className="mb-0 fw-bold text-dark">
                  Recent Projects
                </h4>
                <Button
                  color="primary"
                  size="sm"
                  style={{
                    background: 'linear-gradient(135deg, var(--primary-orange) 0%, var(--primary-blue) 100%)',
                    border: 'none'
                  }}
                >
                  View All Projects
                </Button>
              </CardHeader>
              <CardBody className="p-0">
                <div className="table-responsive">
                  <table className="table table-hover mb-0">
                    <thead className="bg-light">
                      <tr>
                        <th className="border-0 py-3 px-4 text-dark">
                          Project Details
                        </th>
                        <th className="border-0 py-3 text-dark">
                          Status
                        </th>
                        <th className="border-0 py-3 text-dark">
                          Progress
                        </th>
                        <th className="border-0 py-3 text-dark">
                          Priority
                        </th>
                        <th className="border-0 py-3 text-dark">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {recentProjects.map((project, index) => (
                        <tr key={project.id} className="border-bottom">
                          <td className="px-4 py-4">
                            <div>
                              <h6 className="mb-1 fw-bold text-dark">
                                {project.name}
                              </h6>
                              <div className="d-flex align-items-center text-muted">
                                <small className="me-3">
                                  <FaUsers className="me-1" size={12} />
                                  {project.client}
                                </small>
                                <small className="me-3">
                                  <FaUserTie className="me-1" size={12} />
                                  {project.team} members
                                </small>
                                <small>
                                  <FaDollarSign className="me-1" size={12} />
                                  {project.budget}
                                </small>
                              </div>
                            </div>
                          </td>
                          <td className="py-4">
                            <Badge
                              className="px-3 py-2"
                              style={{
                                backgroundColor: getStatusColor(project.status),
                                color: 'white',
                                fontSize: '0.75rem'
                              }}
                            >
                              {project.status}
                            </Badge>
                          </td>
                          <td className="py-4">
                            <div className="d-flex align-items-center">
                              <div className="me-3" style={{ width: '100px' }}>
                                <Progress
                                  value={project.progress}
                                  color={project.status === 'Completed' ? 'success' : 'primary'}
                                  style={{ height: '8px' }}
                                />
                              </div>
                              <small className="text-muted fw-bold">{project.progress}%</small>
                            </div>
                          </td>
                          <td className="py-4">
                            <Badge
                              className="px-2 py-1"
                              style={{
                                backgroundColor: `${getPriorityColor(project.priority)}20`,
                                color: getPriorityColor(project.priority),
                                fontSize: '0.7rem'
                              }}
                            >
                              {project.priority}
                            </Badge>
                          </td>
                          <td className="py-4">
                            <div className="d-flex gap-2">
                              <Button
                                size="sm"
                                color="light"
                                className="p-2 border bg-white"
                                title="View Details"
                              >
                                <FaEye size={12} />
                              </Button>
                              <Button
                                size="sm"
                                color="light"
                                className="p-2 border bg-white"
                                title="Edit Project"
                              >
                                <FaEdit size={12} />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardBody>
            </Card>
          </Col>

        </Row>
      </Container>
    </div>
  );
};

export default Dashboard;
