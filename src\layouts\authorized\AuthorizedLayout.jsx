import { useState } from 'react';
import { Outlet } from 'react-router-dom';
import AuthorizedSidebar from '../../components/authorized/AuthorizedSidebar';
import AuthorizedHeader from '../../components/authorized/AuthorizedHeader';

const AuthorizedLayout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <div
      className="authorized-layout dashboard-layout d-flex"
      style={{
        background: 'var(--lighter)',
        minHeight: '100vh'
      }}
    >
      <AuthorizedSidebar isOpen={sidebarOpen} toggle={toggleSidebar} />

      <div className="authorized-main-content flex-grow-1">
        <AuthorizedHeader toggleSidebar={toggleSidebar} />

        <main
          className="authorized-content"
          style={{
            marginLeft: window.innerWidth >= 768 ? '250px' : '0',
            padding: '20px',
            background: 'var(--lighter)',
            minHeight: 'calc(100vh - 80px)',
            transition: 'margin-left 0.3s ease'
          }}
        >
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default AuthorizedLayout;
